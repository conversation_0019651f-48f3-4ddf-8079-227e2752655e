.expression-helper {
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}

#expression-wrapper {
    display: flex;
    height: calc(100vh - var(--topBarBlockSize));
    width: 100vw;
}

#visual-novel-wrapper {
    display: flex;
    height: calc(100vh - var(--topBarBlockSize));
    width: 100vw;
    overflow: hidden;
}

#visual-novel-wrapper .expression-holder {
    width: max-content;
    display: flex;
    left: unset;
    right: unset;
}

#visual-novel-wrapper .hidden {
    display: none !important;
    visibility: hidden !important;
}

/*#visual-novel-wrapper img.expression {
    object-fit: cover;
}*/

.expression-holder {
    min-width: 100px;
    min-height: 100px;
    max-height: 90vh;
    max-width: 90vh;
    width: calc((100vw - var(--sheldWidth)) /2);
    position: absolute;
    bottom: 0;
    padding: 0;
    left: 0;
    filter: drop-shadow(2px 2px 2px #51515199);
    z-index: 2;
    overflow: hidden;
    resize: both;
    display: flex;
    justify-content: center;
}

#no_chat_expressions {
    text-align: center;
    margin: 10px 0;
    font-weight: bold;
    opacity: 0.8;
}

#image_list_header_name {
    font-weight: 400;
}

img.expression {
    min-width: 100px;
    min-height: 100px;
    max-height: 90vh;
    max-width: 90vh;
    top: 0;
    bottom: 0;
    padding: 0;
    vertical-align: bottom;
    object-fit: contain;
}

img.expression[src=""] {
    visibility: hidden;
}

img.expression.default {
    vertical-align: middle;
    max-height: 120px;
    object-fit: contain !important;
    margin-top: 50px;
}

.expression-clone {
    position: absolute;
}

.debug-image {
    display: none;
    visibility: collapse;
    opacity: 0;
    width: 0px;
    height: 0px;
}

.expression_list_item {
    position: relative;
    max-width: 20%;
    min-width: 100px;
    max-height: 200px;
    background-color: #515151b0;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.expression_list_image_container {
    overflow: hidden;
}

.expression_list_title {
    position: absolute;
    bottom: 0;
    left: 0;
    text-align: center;
    font-weight: 600;
    background-color: #000000a8;
    width: 100%;
    height: 20%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    line-height: 1;
}
.expression_list_custom {
    font-size: 0.66rem;
}

.expression_list_buttons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    /* height: 20%; */
    padding: 0.25rem;
}

.menu_button.expression_list_delete,
.menu_button.expression_list_upload {
    margin-top: 0;
    margin-bottom: 0;
}

.expression_list_image {
    max-width: 100%;
    height: 100%;
    object-fit: cover;
}

#image_list {
    display: flex;
    flex-direction: row;
    column-gap: 1rem;
    margin: 1rem;
    flex-wrap: wrap;
    justify-content: space-evenly;
    row-gap: 1rem;
}

#image_list .expression_list_item[data-expression-type="success"] .expression_list_title {
    color: green;
}

#image_list .expression_list_item[data-expression-type="additional"] .expression_list_title {
    color: darkolivegreen;
}
#image_list .expression_list_item[data-expression-type="additional"] .expression_list_title::before {
    content: '➕';
    position: absolute;
    top: -7px;
    left: -9px;
    font-size: 14px;
    color: transparent;
    text-shadow: 0 0 0 darkolivegreen;
}

#image_list .expression_list_item[data-expression-type="failure"] .expression_list_title {
    color: red;
}

.expression_settings label {
    display: flex;
    align-items: center;
    flex-direction: row;
    margin-left: 0px;
}

.expression_settings label input {
    margin-left: 0px !important;
}

.expression_buttons .menu_button {
    width: fit-content;
    display: flex;
    gap: 10px;
    align-items: baseline;
    flex-direction: row;
}

#expressions_container:has(#expressions_allow_multiple:not(:checked)) #image_list .expression_list_item[data-expression-type="additional"],
#expressions_container:has(#expressions_allow_multiple:not(:checked)) label[for="expressions_reroll_if_same"] {
    opacity: 0.3;
    transition: opacity var(--animation-duration) ease;
}
#expressions_container:has(#expressions_allow_multiple:not(:checked)) #image_list .expression_list_item[data-expression-type="additional"]:hover,
#expressions_container:has(#expressions_allow_multiple:not(:checked)) #image_list .expression_list_item[data-expression-type="additional"]:focus {
    opacity: unset;
}
