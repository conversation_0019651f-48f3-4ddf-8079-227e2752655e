/**
 * 国产模型API支持
 * 支持智谱AI、通义千问、文心一言、RWKV等国产模型
 */

import { SECRET_KEYS, secret_state, writeSecret } from './secrets.js';
import { saveSettingsDebounced } from './power-user.js';

// 国产模型API设置
export const chinese_models_settings = {
    zhipuai_model: 'glm-4-plus',
    qianwen_model: 'qwen-turbo',
    wenxin_model: 'ernie-3.5-8k',
    rwkv_model: '',
    rwkv_url: 'http://127.0.0.1:8000',
};

/**
 * 智谱AI连接处理
 */
async function connectZhipuAI() {
    const api_key = String($('#api_key_zhipuai_main').val()).trim();
    const model = String($('#model_zhipuai_main_select').val());

    if (!api_key) {
        alert('请输入智谱AI API密钥');
        return;
    }

    // 保存API密钥
    await writeSecret(SECRET_KEYS.ZHIPUAI, api_key);

    // 保存模型设置
    chinese_models_settings.zhipuai_model = model;
    await saveSettingsDebounced();

    // 测试连接
    try {
        const response = await fetch('/api/openai/status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                chat_completion_source: 'zhipuai',
                model: model,
            }),
        });

        if (response.ok) {
            $('.online_status_text').text(`智谱AI (${model})`);
            $('.online_status_indicator').removeClass('redtextcolor').addClass('greentextcolor');
            $('#api_key_zhipuai_main').val('');
        } else {
            $('.online_status_text').text('连接失败');
            $('.online_status_indicator').removeClass('greentextcolor').addClass('redtextcolor');
            alert('智谱AI连接失败，请检查API密钥');
        }
    } catch (error) {
        console.error('智谱AI连接错误:', error);
        $('.online_status_text').text('连接错误');
        $('.online_status_indicator').removeClass('greentextcolor').addClass('redtextcolor');
        alert('智谱AI连接错误');
    }
}

/**
 * 通义千问连接处理
 */
async function connectQianwen() {
    const api_key = String($('#api_key_qianwen_main').val()).trim();
    const model = String($('#model_qianwen_main_select').val());

    if (!api_key) {
        alert('请输入通义千问API密钥');
        return;
    }

    // 保存API密钥
    await writeSecret(SECRET_KEYS.QIANWEN, api_key);

    // 保存模型设置
    chinese_models_settings.qianwen_model = model;
    await saveSettingsDebounced();

    // 测试连接
    try {
        const response = await fetch('/api/openai/status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                chat_completion_source: 'qianwen',
                model: model,
            }),
        });

        if (response.ok) {
            $('.online_status_text').text(`通义千问 (${model})`);
            $('.online_status_indicator').removeClass('redtextcolor').addClass('greentextcolor');
            $('#api_key_qianwen_main').val('');
        } else {
            $('.online_status_text').text('连接失败');
            $('.online_status_indicator').removeClass('greentextcolor').addClass('redtextcolor');
            alert('通义千问连接失败，请检查API密钥');
        }
    } catch (error) {
        console.error('通义千问连接错误:', error);
        $('.online_status_text').text('连接错误');
        $('.online_status_indicator').removeClass('greentextcolor').addClass('redtextcolor');
        alert('通义千问连接错误');
    }
}

/**
 * 文心一言连接处理
 */
async function connectWenxin() {
    const api_key = String($('#api_key_wenxin_main').val()).trim();
    const model = String($('#model_wenxin_main_select').val());

    if (!api_key) {
        alert('请输入文心一言API密钥(Access Token)');
        return;
    }

    // 保存API密钥
    await writeSecret(SECRET_KEYS.WENXIN, api_key);

    // 保存模型设置
    chinese_models_settings.wenxin_model = model;
    await saveSettingsDebounced();

    // 测试连接
    try {
        const response = await fetch('/api/openai/status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                chat_completion_source: 'wenxin',
                model: model,
            }),
        });

        if (response.ok) {
            $('.online_status_text').text(`文心一言 (${model})`);
            $('.online_status_indicator').removeClass('redtextcolor').addClass('greentextcolor');
            $('#api_key_wenxin_main').val('');
        } else {
            $('.online_status_text').text('连接失败');
            $('.online_status_indicator').removeClass('greentextcolor').addClass('redtextcolor');
            alert('文心一言连接失败，请检查API密钥');
        }
    } catch (error) {
        console.error('文心一言连接错误:', error);
        $('.online_status_text').text('连接错误');
        $('.online_status_indicator').removeClass('greentextcolor').addClass('redtextcolor');
        alert('文心一言连接错误');
    }
}

/**
 * RWKV连接处理
 */
async function connectRWKV() {
    const api_url = String($('#rwkv_api_url_main').val()).trim();
    const model = String($('#rwkv_model_main').val());

    if (!api_url) {
        alert('请输入RWKV API URL');
        return;
    }

    // 保存设置
    chinese_models_settings.rwkv_url = api_url;
    chinese_models_settings.rwkv_model = model;
    await saveSettingsDebounced();

    // 测试连接
    try {
        const response = await fetch('/api/backends/text-completions/status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                api_server: api_url,
                api_type: 'rwkv',
            }),
        });

        if (response.ok) {
            const data = await response.json();
            $('.online_status_text').text(`RWKV (${model || 'Connected'})`);
            $('.online_status_indicator').removeClass('redtextcolor').addClass('greentextcolor');

            // 加载模型列表
            if (data?.data && Array.isArray(data.data)) {
                loadRWKVModels(data.data);
            }
        } else {
            $('.online_status_text').text('连接失败');
            $('.online_status_indicator').removeClass('greentextcolor').addClass('redtextcolor');
            alert('RWKV连接失败，请检查API URL');
        }
    } catch (error) {
        console.error('RWKV连接错误:', error);
        $('.online_status_text').text('连接错误');
        $('.online_status_indicator').removeClass('greentextcolor').addClass('redtextcolor');
        alert('RWKV连接错误');
    }
}

/**
 * 加载RWKV模型列表
 */
function loadRWKVModels(models) {
    const select = $('#rwkv_model_main');
    select.empty();

    for (const model of models) {
        const option = document.createElement('option');
        option.value = model.id || model.name || model;
        option.text = model.name || model.id || model;
        select.append(option);
    }

    if (chinese_models_settings.rwkv_model) {
        select.val(chinese_models_settings.rwkv_model);
    }
}

/**
 * 初始化国产模型API
 */
export function initChineseModels() {
    // 绑定连接按钮事件
    $('#api_button_zhipuai').on('click', connectZhipuAI);
    $('#api_button_qianwen').on('click', connectQianwen);
    $('#api_button_wenxin').on('click', connectWenxin);
    $('#api_button_rwkv').on('click', connectRWKV);

    // 绑定模型选择事件
    $('#model_zhipuai_main_select').on('change', function() {
        chinese_models_settings.zhipuai_model = String($(this).val());
        saveSettingsDebounced();
    });

    $('#model_qianwen_main_select').on('change', function() {
        chinese_models_settings.qianwen_model = String($(this).val());
        saveSettingsDebounced();
    });

    $('#model_wenxin_main_select').on('change', function() {
        chinese_models_settings.wenxin_model = String($(this).val());
        saveSettingsDebounced();
    });

    $('#rwkv_model_main').on('change', function() {
        chinese_models_settings.rwkv_model = String($(this).val());
        saveSettingsDebounced();
    });

    $('#rwkv_api_url_main').on('input', function() {
        chinese_models_settings.rwkv_url = String($(this).val());
        saveSettingsDebounced();
    });

    console.log('国产模型API初始化完成');
}

/**
 * 加载国产模型设置
 */
export function loadChineseModelsSettings(settings) {
    if (settings.chinese_models) {
        Object.assign(chinese_models_settings, settings.chinese_models);
    }

    // 更新UI
    $('#model_zhipuai_main_select').val(chinese_models_settings.zhipuai_model);
    $('#model_qianwen_main_select').val(chinese_models_settings.qianwen_model);
    $('#model_wenxin_main_select').val(chinese_models_settings.wenxin_model);
    $('#rwkv_model_main').val(chinese_models_settings.rwkv_model);
    $('#rwkv_api_url_main').val(chinese_models_settings.rwkv_url);
}

/**
 * 保存国产模型设置
 */
export function saveChineseModelsSettings() {
    return {
        chinese_models: chinese_models_settings,
    };
}
