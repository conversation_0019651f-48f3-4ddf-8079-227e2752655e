<div>
    <h3 data-i18n="Included settings:">Included settings:</h3>
    <div class="justifyLeft flex-container flexFlowColumn flexNoGap">
        {{#each settings}}
        <label class="checkbox_label">
            <input type="checkbox" value="{{@key}}" name="exclude"{{#if this}} checked{{/if}}>
            <span data-i18n="{{@key}}">{{@key}}</span>
        </label>
        {{/each}}
    </div>
    <h3 data-i18n="Profile name:">Profile name:</h3>
</div>
