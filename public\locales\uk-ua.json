{"Favorite": "улюблений", "Tag": "Тег", "Duplicate": "дуб<PERSON><PERSON><PERSON><PERSON>т", "Persona": "Персона", "Delete": "Видалити", "AI Response Configuration": "Конфігурація відповіді ШІ", "AI Configuration panel will stay open": "Панель конфігурації ШІ залишиться відкритою", "clickslidertips": "Натисніть, щоб ввести значення вручну.", "MAD LAB MODE ON": "УВІМКНЕНО РЕЖИМ БОЖЕНОЇ ЛАБОРАТОРІЇ", "Documentation on sampling parameters": "Документація щодо параметрів вибірки", "kobldpresets": "Налаштування Kobold", "guikoboldaisettings": "З інтерфейсу KoboldAI", "Update current preset": "Оновити поточний шаблон", "Save preset as": "Зберегти шаблон як", "Import preset": "Імпортувати шаблон", "Export preset": "Експортувати шаблон", "Restore current preset": "Відновити поточні налаштування", "Delete the preset": "Видалити шаблон", "novelaipresets": "Налаштування NovelAI", "Default": "За замовчуванням", "openaipresets": "Налаштування OpenAI", "Text Completion presets": "Налаштування Text Completion", "AI Module": "Мод<PERSON><PERSON>ь ШІ", "Changes the style of the generated text.": "Змінює стиль створеного тексту.", "No Module": "Немає модуля", "Instruct": "Інструктувати", "Prose Augmenter": "Зб<PERSON>л<PERSON>шувач прози", "Text Adventure": "Текстові пригоди", "response legth(tokens)": "Відповідь (токени)", "Streaming": "Потокова передача", "Streaming_desc": "Поступово відображати відповідь по мірі її створення", "context size(tokens)": "Контекст (токени)", "unlocked": "Розблоковано", "Only enable this if your model supports context sizes greater than 8192 tokens": "Увімкніть це лише в разі підтримки моделлю розмірів контексту більше 8192 токенів", "Max prompt cost:": "Максимальна оперативна вартість:", "Display the response bit by bit as it is generated.": "Показувати відповідь по бітах по мірі її генерації.", "When this is off, responses will be displayed all at once when they are complete.": "Коли це вимкнено, відповіді будуть відображатися разом, коли вони будуть завершені.", "Temperature": "Температура", "rep.pen": "Штра<PERSON> за повтор", "Rep. Pen. Range.": "Діапазон штрафу за повторення.", "Rep. Pen. Slope": "Кутна нахилу штрафу за повтор", "Rep. Pen. Freq.": "Частота штрафу за повторення.", "Rep. Pen. Presence": "Наявність штрафу за повторення.", "TFS": "TFS", "Phrase Repetition Penalty": "Штраф за повтор фраз", "Off": "Вимкнено", "Very light": "Дуже легкий", "Light": "Легкий", "Medium": "Середній", "Aggressive": "Агресивний", "Very aggressive": "Дуже агресивний", "Unlocked Context Size": "Розблокований розмір контексту", "Unrestricted maximum value for the context slider": "Необмежене максимальне значення для повзунка контексту", "Context Size (tokens)": "Розмір контексту (токени)", "Max Response Length (tokens)": "<PERSON><PERSON><PERSON><PERSON><PERSON> відповіді (токени)", "Multiple swipes per generation": "Кілька свайпів за покоління", "Enable OpenAI completion streaming": "Увімкнути потокове завершення OpenAI", "Frequency Penalty": "Штраф за частоту", "Presence Penalty": "Штраф за наявність", "Count Penalty": "Рахувати пенальті", "Top K": "Топ K", "Top P": "Топ P", "Repetition Penalty": "Штраф за повторення", "Min P": "<PERSON><PERSON><PERSON> <PERSON>", "Top A": "Топ A", "Quick Prompts Edit": "Швидке редагування підказок", "Main": "Головний", "NSFW": "NSFW", "Jailbreak": "Джейлбрейк", "Utility Prompts": "Допоміжні підказки", "Impersonation prompt": "Запит на перевтілення", "Restore default prompt": "Відновити типовий запит", "Prompt that is used for Impersonation function": "Запит, який використовується для функції перевтілення", "World Info Format Template": "Шаблон формату World Info", "Restore default format": "Відновити стандартний формат", "Wraps activated World Info entries before inserting into the prompt.": "Обтікає активовані записи World Info перед вставленням у підказку.", "scenario_format_template_part_1": "використання", "scenario_format_template_part_2": "щоб позначити місце, куди вставляється вміст.", "Scenario Format Template": "Шаблон формату сценарію", "Personality Format Template": "Шаблон формату особистості", "Group Nudge Prompt Template": "Шаблон підказки Group Nudge", "Sent at the end of the group chat history to force reply from a specific character.": "Надсилається в кінці історії групового чату, щоб примусово відповісти від певного персонажа.", "New Chat": "<PERSON>овий чат", "Restore new chat prompt": "Відновити новий чат", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "Встановіть на початку історії чату, щоб вказати, що незабаром розпочнеться новий чат.", "New Group Chat": "Новий груповий чат", "Restore new group chat prompt": "Відновити запит за замовчуванням", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "Установіть на початку історії чату, щоб вказати, що незабаром розпочнеться новий груповий чат.", "New Example Chat": "Новий приклад чату", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "Встановіть на початку прикладів діалогу, щоб вказати, що незабаром розпочнеться новий приклад чату.", "Continue nudge": "Продовжуйте штовхати", "Set at the end of the chat history when the continue button is pressed.": "Встановлюється в кінці історії чату, коли натискається кнопка продовження.", "Replace empty message": "Замінити порожнє повідомлення", "Send this text instead of nothing when the text box is empty.": "Надіслати цей текст замість порожнього, коли поле тексту порожнє.", "Seed": "Зерно", "Set to get deterministic results. Use -1 for random seed.": "Налаштувати для отримання детермінованих результатів. Використовуйте -1 для випадкового початкового числа.", "Temperature controls the randomness in token selection": "Температура контролює випадковість у виборі токенів", "Top_K_desc": "Top K встановлює максимальну кількість наймовірніших токенів, які можна обрати", "Top_P_desc": "Top P (також відомий як відбір ядра)", "Typical P": "Типове P", "Typical_P_desc": "Типовий відбір P визначає пріоритет токенів на основі їх відхилення від середньої ентропії набору", "Min_P_desc": "Min P встановлює базову мінімальну ймовірність", "Top_A_desc": "Top A встановлює поріг для вибору токенів на основі квадрату найвищої ймовірності токена", "Tail_Free_Sampling_desc": "Безхвостовий відбір (TFS)", "rep.pen range": "Діапазон штрафу за повтор", "Mirostat": "Міростат", "Mode": "Режим", "Mirostat_Mode_desc": "Значення 0 повністю вимикає Mirostat. 1 – для Mirostat 1.0, а 2 – для Mirostat 2.0", "Tau": "Тау", "Mirostat_Tau_desc": "Контролює змінність виходів Mirostat", "Eta": "Ета", "Mirostat_Eta_desc": "Контролює швидкість навчання Mirostat", "Ban EOS Token": "Заборонити токен EOS", "Ban_EOS_Token_desc": "Заборонити токен кінця послідовності (EOS) за допомогою KoboldCpp (і, можливо, також інші токени за допомогою KoboldAI).\rДобре підходить для написання історій, але не слід використовувати для режиму чату та інструктажу.", "GBNF Grammar": "Синтаксис GBNF", "Type in the desired custom grammar": "Введіть бажаний власний синтаксис", "Samplers Order": "Порядок вибірки", "Samplers will be applied in a top-down order. Use with caution.": "Вибірки будуть застосовані у порядку зверху донизу. Використовуйте з обережністю.", "Tail Free Sampling": "Безхвостовий відбір", "Load koboldcpp order": "Завантажити порядок koboldcpp", "Preamble": "Преамбула", "Use style tags to modify the writing style of the output.": "Використовуйте теги стилю для зміни стилю написання виводу.", "Banned Tokens": "Заборонені токени", "Sequences you don't want to appear in the output. One per line.": "Послідовності, які ви не хочете бачити у виводі. Одна на рядок.", "Logit Bias": "Зміщення логітів", "Add": "Додати", "Helps to ban or reenforce the usage of certain words": "Допомагає забороняти або підсилювати використання певних слів", "CFG Scale": "Масштаб CFG", "Negative Prompt": "Нега<PERSON>ивна підказка", "Add text here that would make the AI generate things you don't want in your outputs.": "Додайте сюди текст, який змусить штучний інтелект генерувати речі, які ви не хочете бачити у виводах.", "Used if CFG Scale is unset globally, per chat or character": "Використовується, якщо масштаб CFG не встановлений глобально, на кожен чат або персонажа.", "Mirostat Tau": "Тау Mirostat", "Mirostat LR": "Міростат ЛР", "Min Length": "Мінімальна довжина", "Top K Sampling": "Вибірка топ K", "Nucleus Sampling": "Вибірка ядра", "Top A Sampling": "Вибірка топ A", "CFG": "CFG", "Neutralize Samplers": "Нейтралізувати вибірку", "Set all samplers to their neutral/disabled state.": "Встановити всі семплери у їх нейтральний/вимкнений стан.", "Sampler Select": "Виб<PERSON>р семплера", "Customize displayed samplers or add custom samplers.": "Налаштуйте відображувані семплери або додайте власні семплери.", "Epsilon Cutoff": "Від<PERSON><PERSON><PERSON> епсилону", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "Епсилон встановлює нижню межу ймовірності, нижче якої токени виключаються з вибірки", "Eta Cutoff": "Від<PERSON><PERSON><PERSON> ети", "Eta_Cutoff_desc": "Eta-відсічення - основний параметр спеціальної техніки вибірки Ета.&#13;У одиницях 1e-4; розумна величина - 3.&#13;Встановіть 0, щоб вимкнути.&#13;Див. статтю «Вибірка відсічення як модель мовного розподілення» Хевітта та ін. (2022) для деталей.", "rep.pen decay": "Rep <PERSON>", "Encoder Rep. Pen.": "Штра<PERSON> за повтор кодера", "No Repeat Ngram Size": "Розмір n-грам без повторень", "Skew": "Пере<PERSON><PERSON><PERSON>", "Max Tokens Second": "Максима<PERSON>ьна кількість токенів / секунду", "Smooth Sampling": "Пла<PERSON>на вибірка", "Smooth_Sampling_desc": "Дозволяє використовувати квадратичні/кубічні перетворення для коригування розподілу. Нижчі значення коефіцієнта згладжування будуть більш креативними, зазвичай між 0,2-0,3 є найкращою точкою (припускаючи, що крива = 1). Вищі значення кривої згладжування зроблять криву крутішою, що агресивніше каратиме вибір із низькою ймовірністю. Крива 1.0 еквівалентна лише використанню коефіцієнта згладжування.", "Smoothing Factor": "Коефіцієнт згладжування", "Smoothing Curve": "Крива згладжування", "DRY_Repetition_Penalty_desc": "DRY штрафує токени, які розширюють кінець вхідних даних у послідовність, яка раніше траплялася у вхідних даних. Встановіть множник на 0, щоб вимкнути.", "DRY Repetition Penalty": "Штраф за СУХЕ повторення", "DRY_Multiplier_desc": "Встановіть значення > 0, щоб увімкнути DRY. Контролює величину штрафу для найкоротших штрафних послідовностей.", "Multiplier": "Множник", "DRY_Base_desc": "Контролює швидкість збільшення штрафу зі збільшенням довжини послідовності.", "Base": "База", "DRY_Allowed_Length_desc": "Найдовша послідовність, яку можна повторити без покарання.", "Allowed Length": "Дозволена довжина", "Penalty Range": "Діа<PERSON>азон пенальті", "DRY_Sequence_Breakers_desc": "Токен<PERSON>, зб<PERSON>г яких не триває. Задається як список рядків у лапках, розділених комами.", "Sequence Breakers": "Порушники послідовності", "JSON-serialized array of strings.": "JSON-серіалізований масив рядків.", "Dynamic Temperature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>на температура", "Scale Temperature dynamically per token, based on the variation of probabilities": "Шкала температури динамічно за кожний токен, на основі варіації ймовірностей", "Minimum Temp": "Мінімальна температура", "Maximum Temp": "Макси<PERSON>альна температура", "Exponent": "Експонента", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (режим=1 тільки для llama.cpp)", "Mirostat_desc": "Mirostat - це термостат для заплутанності виводу", "Mirostat Mode": "Режим Mirostat", "Variability parameter for Mirostat outputs": "Параметр змінності для виходів Mirostat", "Mirostat Eta": "Ета Mirostat", "Learning rate of Mirostat": "Швидкість навчання Mirostat", "Beam search": "Пучковий пошук", "Helpful tip coming soon.": "Корисна порада незабаром.", "Number of Beams": "Кількість пучків", "Length Penalty": "Штраф за довжину", "Early Stopping": "Раннє зупинення", "Contrastive search": "Контрастний пошук", "Penalty Alpha": "Коефіцієнт штрафу", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "Сила терміну регуляризації контрастного пошуку. Встановіть 0, щоб вимкнути контрастний пошук", "Do Sample": "Робити відбір", "Add BOS Token": "Додати токен BOS", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "Додавати bos_token на початок запиту. Вимкнення цього може зробити відповіді більш креативними", "Ban the eos_token. This forces the model to never end the generation prematurely": "Заборонити токен eos. Це змусить модель ніколи не завершувати генерацію передчасно", "Ignore EOS Token": "Ігнорувати токен EOS", "Ignore the EOS Token even if it generates.": "Ігноруйте токен EOS, навіть якщо він генерується.", "Skip Special Tokens": "Пропустити спеціальні токени", "Temperature Last": "Температура останньою", "Temperature_Last_desc": "Використовувати вибірку по температурі останньою", "Speculative Ngram": "Спекулятив<PERSON><PERSON> Ngram", "Use a different speculative decoding method without a draft model": "Використовуйте інший спекулятивний метод декодування без чорнової моделі.\rБажано використовувати чорнову модель. Спекулятивна ngram не така ефективна.", "Spaces Between Special Tokens": "Проміжки між спеціальними маркерами", "LLaMA / Mistral / Yi models only": "Тільки моделі LLaMA / Mistral / Yi", "Example: some text [42, 69, 1337]": "Приклад: деякий текст [42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "Вільні інструкції класифікатора. Більше корисних порад незабаром", "Scale": "Масш<PERSON><PERSON><PERSON>", "JSON Schema": "Схема JSON", "Type in the desired JSON schema": "Введіть потрібну схему JSON", "Grammar String": "Граматичний рядок", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF або EBNF, залежить від серверної частини, яка використовується. Якщо ви використовуєте це, ви повинні знати, який.", "Top P & Min P": "Верхній P & Min P", "Load default order": "Завантажити типовий порядок", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "лише llama.cpp. Визначає порядок пробовідбірників. Якщо режим Mirostat не 0, порядок вибірки ігнорується.", "Sampler Priority": "Пріоритет вибірки", "Ooba only. Determines the order of samplers.": "Лише Ooba. Визначає порядок вибірки.", "Character Names Behavior": "Поведінка імен персонажів", "Helps the model to associate messages with characters.": "Допомагає моделі пов’язувати повідомлення з символами.", "None": "Немає", "character_names_default": "За винятком груп і минулих персонажів. В іншому випадку переконайтеся, що ви вказали імена в підказці.", "Don't add character names.": "Не додавайте імена персонажів.", "Completion": "Об'єкт завершення", "character_names_completion": "Застосовуються обмеження: лише латинські букви та цифри підкреслення. Працює не для всіх джерел, зокрема: Claude, MistralAI, Google.", "Add character names to completion objects.": "Додайте імена персонажів до об’єктів завершення.", "Message Content": "Вміст повідомлення", "Prepend character names to message contents.": "Додайте імена символів до вмісту повідомлення.", "Continue Postfix": "Продовжити Postfix", "The next chunk of the continued message will be appended using this as a separator.": "Наступний фрагмент продовженого повідомлення буде додано з використанням цього як роздільника.", "Space": "Простір", "Newline": "Новий рядок", "Double Newline": "Подвійний новий рядок", "Wrap user messages in quotes before sending": "Перед відправленням обгортати повідомлення користувача в лапки", "Wrap in Quotes": "Обертати у лапки", "Wrap entire user message in quotes before sending.": "Перед відправкою повідомлення користувача обертати усе у лапки.", "Leave off if you use quotes manually for speech.": "Не включайте, якщо ви вручну використовуєте лапки для мовлення.", "Continue prefill": "Продовжувати автозаповнення", "Continue sends the last message as assistant role instead of system message with instruction.": "Продовженням буде відправлена остання повідомлення як роль асистента, а не системне повідомлення з інструкцією.", "Squash system messages": "Заповнювати системні повідомлення", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "Об'єднує послідовні системні повідомлення в одне (крім прикладів діалогів). Може покращити співпрацю для деяких моделей.", "Enable function calling": "Увімкнути виклик функцій", "Send inline images": "Надсилати вбудовані зображення", "image_inlining_hint_1": "Надсилає зображення у підказках, якщо модель це підтримує.\n                                                Використовувати", "image_inlining_hint_2": "дії з будь-яким повідомленням або", "image_inlining_hint_3": "меню, щоб прикріпити файл зображення до чату.", "Inline Image Quality": "Якість вбудованого зображення", "openai_inline_image_quality_auto": "Авто", "openai_inline_image_quality_low": "Низький", "openai_inline_image_quality_high": "Високий", "Use AI21 Tokenizer": "Використовуйте AI21 Tokenizer", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "Використовуйте відповідний токенізатор для моделей Jurassic, який ефективніший, ніж GPT.", "Use Google Tokenizer": "Використовувати токенізатор Google", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "Використовуйте відповідний токенізатор для моделей Google через їх API. Повільніша обробка підказок, але пропонує набагато точніше підрахунку токенів.", "Use system prompt": "Використовуйте системну підказку", "(Gemini 1.5 Pro/Flash only)": "(тільки Gemini 1.5 Pro/Flash)", "Merges_all_system_messages_desc_1": "Об’єднує всі системні повідомлення до першого повідомлення з несистемною роллю та надсилає їх у a", "Merges_all_system_messages_desc_2": "поле.", "Assistant Prefill": "Асистент автозаповнення", "Start Claude's answer with...": "Почати відповідь Клода з...", "Assistant Impersonation Prefill": "Попереднє заповнення уособлення помічника", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "Надсилати системний промпт для підтримуваних моделей. Якщо відключено, повідомлення користувача додається в початок промпта.", "User first message": "Перше повідомлення користувача", "Restore User first message": "Відновити перше повідомлення користувача", "Human message": "Людське повідомлення, інструкція тощо.\nНе додає нічого, якщо пусте, тобто вимагає нового запиту з роллю «користувач».", "New preset": "Новий шаблон", "Delete preset": "Видалити шаблон", "View / Edit bias preset": "Переглянути / Редагувати налаштування зміщення", "Add bias entry": "Додати запис зміщення", "Most tokens have a leading space.": "Більшість жетонів мають пробіл на початку.", "API Connections": "З'єднання з API", "Text Completion": "Завершення тексту", "Chat Completion": "Завершення чату", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "Уникайте надсилання чутливої інформації в Horde.", "Review the Privacy statement": "Перегляньте заяву про конфіденційність", "Register a Horde account for faster queue times": "Зареєструйте обліковий запис Horde для швидшого часу очікування в черзі", "Learn how to contribute your idle GPU cycles to the Horde": "Дізнайтеся, як сприяти внеском вашого неактивного циклу GPU до горди", "Adjust context size to worker capabilities": "Налаштовувати розмір контексту відповідно до можливостей робітників", "Adjust response length to worker capabilities": "Налаштовувати довжину відповіді відповідно до можливостей робітників", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "Може допомогти з поганими відповідями, ставлячи в чергу тільки схвалених робітників. Може сповільнити час відповіді.", "Trusted workers only": "Лише довірені працівники", "API key": "Ключ API", "Get it here:": "Отримайте його тут:", "Register": "Зареєструвати", "View my Kudos": "Переглянути мої Kudos", "Enter": "Увійти", "to use anonymous mode.": "щоб використовувати анонімний режим.", "Clear your API key": "Очистити свій ключ API", "For privacy reasons, your API key will be hidden after you reload the page.": "З причин приватності ваш ключ API буде приховано після перезавантаження сторінки.", "Models": "Моделі", "Refresh models": "Оновити моделі", "-- Horde models not loaded --": "-- Мо<PERSON><PERSON><PERSON><PERSON> Horde не завантажені --", "Not connected...": "Не підключено...", "API url": "URL-адреса API", "Example: http://127.0.0.1:5000/api ": "Приклад: http://127.0.0.1:5000/api", "Connect": "Підключитися", "Cancel": "Скасувати", "Novel API key": "Ключ API для NovelAI", "Get your NovelAI API Key": "Отримайте свій ключ API NovelAI", "Enter it in the box below": "Введіть його в поле нижче", "Novel AI Model": "Модель NovelAI", "No connection...": "Немає підключення...", "API Type": "Тип <PERSON>", "Default (completions compatible)": "За замовчуванням [сумісні з OpenAI /доповненнями: oobabooga, LM Studio тощо]", "TogetherAI API Key": "Ключ API для TogetherAI", "TogetherAI Model": "Модель TogetherAI", "-- Connect to the API --": "-- Підлючиться до API --", "OpenRouter API Key": "Ключ API для OpenRouter", "Click Authorize below or get the key from": "Клацніть 'Авторизувати' нижче або отримайте ключ з", "View Remaining Credits": "Переглянути залишкові кредити", "OpenRouter Model": "Модель OpenRouter", "Model Providers": "Модельні постачальники", "InfermaticAI API Key": "Ключ API InfermaticAI", "InfermaticAI Model": "Модель InfermaticAI", "DreamGen API key": "Ключ API DreamGen", "DreamGen Model": "Модель DreamGen", "Mancer API key": "Ключ API для Mancer", "Mancer Model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Make sure you run it with": "Переконайтеся, що ви запускаєте його з", "flag": "прапорцем", "API key (optional)": "Ключ API (необов'язково)", "Server url": "URL-адреса сервера", "Example: http://127.0.0.1:5000": "Приклад: http://127.0.0.1:5000", "Custom model (optional)": "Власна модель (необов'язково)", "vllm-project/vllm": "vllm-project/vllm (режим оболонки OpenAI API)", "vLLM API key": "Ключ API vLLM", "Example: http://127.0.0.1:8000": "Приклад: http://127.0.0.1:8000", "vLLM Model": "Модель vLLM", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (режим OpenAI API)", "Aphrodite API key": "Ключ API для Aphrodite", "Aphrodite Model": "Модель Афродіта", "ggerganov/llama.cpp": "ggerganov/llama.cpp (сервер виведення)", "Example: http://127.0.0.1:8080": "Приклад: http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "Приклад: http://127.0.0.1:11434", "Ollama Model": "М<PERSON>д<PERSON><PERSON><PERSON> Ollama", "Download": "Завант<PERSON><PERSON><PERSON>ти", "Tabby API key": "Ключ API для Tabby", "koboldcpp API key (optional)": "API-клю<PERSON> kobold<PERSON> (необов’язково)", "Example: http://127.0.0.1:5001": "Приклад: http://127.0.0.1:5001", "Authorize": "Авторизувати", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "Отримайте свій токен API OpenRouter за допомогою OAuth. Вас буде перенаправлено на openrouter.ai", "Bypass status check": "Обійти перевірку статусу", "Chat Completion Source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Chat Completion", "Reverse Proxy": "Зворотний проксі", "Proxy Presets": "Попередні налаштування проксі", "Saved addresses and passwords.": "Збережені адреси та паролі.", "Save Proxy": "Зберегти проксі", "Delete Proxy": "Видалити проксі", "Proxy Name": "Ім'я проксі", "This will show up as your saved preset.": "Це відображатиметься як ваш збережений стиль.", "Proxy Server URL": "URL проксі-сервера", "Alternative server URL (leave empty to use the default value).": "URL-адрес альтернативного сервера (залиште порожнім, щоб використовувати значення за замовчуванням).", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "Видаліть свій справжній API ключ OAI з API-панелі ПЕРЕД тим, як щось вводити в це поле", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "Ми не можемо надати підтримку для проблем, які виникають при використанні неофіційного проксі OpenAI", "Doesn't work? Try adding": "не працює? Спробуйте додати", "at the end!": "в кінці!", "Proxy Password": "Пароль проксі", "Will be used as a password for the proxy instead of API key.": "Використовуватиметься як пароль для проксі замість ключа API.", "Peek a password": "Подивіться пароль", "OpenAI API key": "Ключ API для OpenAI", "View API Usage Metrics": "Переглянути метрики використання API", "Follow": "Дотримуйтесь", "these directions": "цих інструкцій", "to get your OpenAI API key.": "щоб отримати свій ключ API для OpenAI.", "Use Proxy password field instead. This input will be ignored.": "Натомість використовуйте поле «Пароль проксі». Це введення буде проігноровано.", "OpenAI Model": "Модель OpenAI", "Bypass API status check": "Обійти перевірку статусу API", "Show External models (provided by API)": "Показати зовнішні моделі (надані API)", "Get your key from": "Отримайте свій ключ з", "Anthropic's developer console": "консолі розробника Anthropic", "Claude Model": "Мод<PERSON><PERSON>ь Claude", "Window AI Model": "Модель Window AI", "Model Order": "Сортування моделі OpenRouter", "Alphabetically": "За алфавітом", "Price": "Ціна (найдешевша)", "Context Size": "Розмір контексту", "Group by vendors": "Групувати за постачальниками", "Group by vendors Description": "Помістіть моделі OpenAI в одну групу, модел<PERSON> Anthropic в іншу групу тощо. Можна поєднати з сортуванням.", "Allow fallback routes": "Дозволити резервні маршрути", "Allow fallback routes Description": "Автоматично вибирає альтернативну модель, якщо вибрана модель не може задовольнити ваш запит.", "AI21 API Key": "Ключ API для AI21", "AI21 Model": "Модель AI21", "Google AI Studio API Key": "Ключ API Google AI Studio", "Google Model": "Модель Google", "MistralAI API Key": "Ключ API MistralAI", "MistralAI Model": "Модель MistralAI", "Groq API Key": "Ключ API Groq", "Groq Model": "Модель Groq", "Perplexity API Key": "Ключ API Perplexity", "Perplexity Model": "Модель здивування", "Cohere API Key": "Ключ API Cohere", "Cohere Model": "Модель Cohere", "Custom Endpoint (Base URL)": "Спеціальна кінцева точка (базова URL-адреса)", "Custom API Key": "Спеціальний ключ API", "Available Models": "Доступні моделі", "Prompt Post-Processing": "Швидка постобробка", "Applies additional processing to the prompt before sending it to the API.": "Застосовує додаткову обробку запиту перед надсиланням його в API.", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "Підтверджує ваше підключення до API, надсилаючи коротке тестове повідомлення. Пам'ятайте, що ви будете платити за це!", "Test Message": "Тестове повідомлення", "Auto-connect to Last Server": "Автоматичне підключення до останнього сервера", "Missing key": "❌ Відсут<PERSON><PERSON>й ключ", "Key saved": "✔️ Ключ збережено", "View hidden API keys": "Переглянути приховані ключі API", "AI Response Formatting": "Форматування відповіді ШІ", "Advanced Formatting": "Розширене форматування", "Context Template": "<PERSON>а<PERSON><PERSON><PERSON><PERSON> контексту", "Auto-select this preset for Instruct Mode": "Автоматично вибрати цей шаблон для режиму інструкцій", "Story String": "Рядок історії", "Example Separator": "Роздільник прикладів", "Chat Start": "Початок чату", "Add Chat Start and Example Separator to a list of stopping strings.": "Додайте початок чату та роздільник прикладів до списку рядків зупинки.", "Use as Stop Strings": "Використовувати як рядки зупинки", "Allow Jailbreak": "Дозволити втечу з в'язниці", "Context Order": "Порядок контексту", "Summary": "Резюме", "Author's Note": "Примітка автора", "Example Dialogues": "Приклади діалогів", "Hint": "Підказка:", "In-Chat Position not affected": "Порядки резюме та примітки автора діють лише тоді, коли для них не встановлено позицію в чаті.", "Instruct Mode": "Режим інструкцій", "Enabled": "Увімкнено", "instruct_bind_to_context": "Якщо ввімкнено, шаблони контексту вибиратимуться автоматично на основі назви вибраного шаблону вказівок або за бажанням.", "Bind to Context": "Прив'язати до контексту", "Presets": "Налаштування", "Auto-select this preset on API connection": "Автоматично вибрати цей шаблон при підключенні до API", "Activation Regex": "Регулярний вираз активації", "Wrap Sequences with Newline": "Починати послідовності з нового рядка", "Replace Macro in Sequences": "Заміняти макроси в послідовностях", "Skip Example Dialogues Formatting": "Пропустити форматування прикладів діалогів", "Include Names": "Включити імена", "Force for Groups and Personas": "Примусово для груп і персон", "System Prompt": "Системне запитання", "Instruct Mode Sequences": "Послідовності режиму інструкцій", "System Prompt Wrapping": "Обгортка системних підказок", "Inserted before a System prompt.": "Вставляється перед системним запитом.", "System Prompt Prefix": "Префікс системної підказки", "Inserted after a System prompt.": "Вставляється після системного запиту.", "System Prompt Suffix": "Суфікс системної підказки", "Chat Messages Wrapping": "Обгортка повідомлень чату", "Inserted before a User message and as a last prompt line when impersonating.": "Вставляється перед повідомленням користувача та як останній рядок підказки під час уособлення.", "User Message Prefix": "Префікс повідомлення користувача", "Inserted after a User message.": "Вставляється після повідомлення користувача.", "User Message Suffix": "Суфікс повідомлення користувача", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "Вставляється перед повідомленням Асистента та як останній рядок підказки під час створення відповіді ШІ.", "Assistant Message Prefix": "Префікс повідомлення помічника", "Inserted after an Assistant message.": "Вставляється після повідомлення Асистента.", "Assistant Message Suffix": "Суфікс повідомлення помічника", "Inserted before a System (added by slash commands or extensions) message.": "Вставляється перед системним повідомленням (додається за допомогою скісної риски або розширень).", "System Message Prefix": "Префікс системного повідомлення", "Inserted after a System message.": "Вставляється після системного повідомлення.", "System Message Suffix": "Суфікс системного повідомлення", "If enabled, System Sequences will be the same as User Sequences.": "Якщо ввімкнено, системні послідовності будуть такими самими, як і послідовності користувача.", "System same as User": "Система така ж, як Користувач", "Misc. Sequences": "Різне Послідовності", "Inserted before the first Assistant's message.": "Вставляється перед першим повідомленням Помічника.", "First Assistant Prefix": "Префікс першого помічника", "instruct_last_output_sequence": "Вставляється перед останнім повідомленням Помічника або як останній рядок підказки під час створення відповіді ШІ (за винятком нейтральної/системної ролі).", "Last Assistant Prefix": "Префікс останнього помічника", "Will be inserted as a last prompt line when using system/neutral generation.": "Буде вставлено як останній рядок підказки під час використання системної/нейтральної генерації.", "System Instruction Prefix": "Префікс системної інструкції", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "Якщо згенеровано стоп-послідовність, усе, що минуло, буде видалено з виводу (включно).", "Stop Sequence": "Послідовність зупинки", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "Буде вставлено на початку історії чату, якщо вона не починається з повідомлення користувача.", "User Filler Message": "Повідомлення користувача", "Context Formatting": "Форматування контексту", "(Saved to Context Template)": "(Зберігаються в шаблоні контексту)", "Always add character's name to prompt": "Завжди додавати ім'я персонажа до запиту", "Generate only one line per request": "Генерувати лише один рядок за запит", "Trim Incomplete Sentences": "Обрізати неповні речення", "Include Newline": "Включити новий рядок", "Misc. Settings": "Різні налаштування", "Collapse Consecutive Newlines": "Згортати послідовні нові рядки", "Trim spaces": "Обрізати пробіли", "Tokenizer": "Токенізатор", "Token Padding": "Набивка токенів", "Start Reply With": "Почати відповідь з", "AI reply prefix": "Префікс відповіді ШІ", "Show reply prefix in chat": "Показати префікс відповіді в чаті", "Non-markdown strings": "Рядки без Markdown", "separate with commas w/o space between": "розділяйте комами без пропусків між ними", "Custom Stopping Strings": "Власні рядки зупинки", "JSON serialized array of strings": "JSON-серіалізований масив рядків", "Replace Macro in Stop Strings": "Замінювати макроси у власних рядках зупинки", "Auto-Continue": "Автоматичне продовження", "Allow for Chat Completion APIs": "Дозволити для Chat Completion API", "Target length (tokens)": "Цільова довжина (токени)", "World Info": "Інформація про світ", "Locked = World Editor will stay open": "Заблоковано = Редактор світу залишиться відкритим", "Worlds/Lorebooks": "Світи / Книги знань", "Active World(s) for all chats": "Активні світи для всіх чатів", "-- World Info not found --": "-- Світи не знайдені --", "Global World Info/Lorebook activation settings": "Налаштування активації Global World Info/Lorebook", "Click to expand": "Натисніть, щоб розгорнути", "Scan Depth": "Гли<PERSON><PERSON>на сканування", "Context %": "Контекст %", "Budget Cap": "<PERSON>і<PERSON><PERSON>т бюджету", "(0 = disabled)": "(0 = вимкнено)", "Scan chronologically until reached min entries or token budget.": "Скануйте в хронологічному порядку, поки не буде досягнуто мінімальних записів або бюджету маркерів.", "Min Activations": "Міні<PERSON><PERSON><PERSON>ьна кількість активацій", "Max Depth": "Максимальна глибина", "(0 = unlimited, use budget)": "(0 = без обмежень, використовуйте бюджет)", "Insertion Strategy": "Стратегія вставки", "Sorted Evenly": "Рівномірно впорядковані", "Character Lore First": "Інформація персонажу першою", "Global Lore First": "Глобальна інформація першою", "Entries can activate other entries by mentioning their keywords": "Записи можуть активувати інші записи, згадуючи їх ключові слова", "Recursive Scan": "Рекурсивне сканування", "Lookup for the entry keys in the context will respect the case": "Пошук ключів запису в контексті буде дотримуватися регістру", "Case Sensitive": "Чутливість до регістру", "If the entry key consists of only one word, it would not be matched as part of other words": "Якщо ключ запису складається лише з одного слова, він не буде співпадати як частина інших слів", "Match Whole Words": "Відповідність цілим словам", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "Для фільтрації групи включення буде вибрано лише записи з найбільшою кількістю збігів ключів", "Use Group Scoring": "Використовуйте групову оцінку", "Alert if your world info is greater than the allocated budget.": "Сповіщайте, якщо інформація про ваш світ перевищує виділений бюджет.", "Alert On Overflow": "Сповіщення при переповненні", "New": "Новий", "or": "або", "--- Pick to Edit ---": "--- Редагувати ---", "Rename World Info": "Перейменувати інформацію про світ", "Open all Entries": "Відкрити всі записи", "Close all Entries": "Закрити всі записи", "New Entry": "Новий запис", "Fill empty Memo/Titles with Keywords": "Заповнити порожні заголовки ключовими словами", "Import World Info": "Імпортувати інформацію про світ", "Export World Info": "Експортувати інформацію про світ", "Duplicate World Info": "Дублювати інформацію про світ", "Delete World Info": "Видалити інформацію про світ", "Search...": "Пошук...", "Search": "По<PERSON><PERSON>к", "Priority": "Пріоритет", "Custom": "Користувацький", "Title A-Z": "Заголовок від А до Я", "Title Z-A": "Заголовок від Я до А", "Tokens ↗": "Токени ↗", "Tokens ↘": "Токени ↘", "Depth ↗": "Глибина ↗", "Depth ↘": "Глибина ↘", "Order ↗": "Порядок ↗", "Order ↘": "Порядок ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "Тригер% ↗", "Trigger% ↘": "Тригер% ↘", "Refresh": "Оновити", "User Settings": "Налаштування користувача", "Simple": "Простий", "Advanced": "Розширений", "UI Language": "Мова", "Account": "Обліковий запис", "Admin Panel": "Панель адміністратора", "Logout": "Вийти", "Search Settings": "Пошук налаштувань", "UI Theme": "Тема інтерфейсу", "Import a theme file": "Імпортувати файл теми", "Export a theme file": "Експортувати файл теми", "Delete a theme": "Видалити тему", "Update a theme file": "Оновити файл теми", "Save as a new theme": "Зберегти як нову тему", "Avatar Style:": "Стиль аватара", "Circle": "Коло", "Square": "Ква<PERSON><PERSON><PERSON><PERSON>", "Rectangle": "Прямокутник", "Chat Style:": "Стиль чату:", "Flat": "Плоский\nБульбашки\nдокумент", "Bubbles": "Бульбашки", "Document": "Документ", "Specify colors for your theme.": "Визначте кольори для вашої теми.", "Theme Colors": "Кольори теми", "Main Text": "Головний текст", "Italics Text": "Курсивний текст", "Underlined Text": "Підкреслений текст", "Quote Text": "Текст в лапках", "Shadow Color": "<PERSON>о<PERSON><PERSON>р тіні", "Chat Background": "Фон чату", "UI Background": "Фон інтерфейсу", "UI Border": "Межі інтерфейсу", "User Message Blur Tint": "Колір повідомлення користувача", "AI Message Blur Tint": "Колір повідомлення ШІ", "Chat Width": "<PERSON><PERSON><PERSON><PERSON><PERSON> чату", "Width of the main chat window in % of screen width": "Ширина головного вікна чату у % ширини екрана", "Font Scale": "Ма<PERSON><PERSON>таб шрифту", "Font size": "Розмір шрифту", "Blur Strength": "Сила розмиття", "Blur strength on UI panels.": "Сила розмиття на панелях інтерфейсу.", "Text Shadow Width": "<PERSON>и<PERSON>ина тіні тексту", "Strength of the text shadows": "Інтенсивність тіні тексту", "Disables animations and transitions": "Вимикає анімації та переходи", "Reduced Motion": "Зменшена рухомість", "removes blur from window backgrounds": "видаляє розмиття з фонів вікон для прискорення відображення", "No Blur Effect": "Без ефекту розмиття", "Remove text shadow effect": "Видалити тінь тексту", "No Text Shadows": "Без тіней тексту", "Reduce chat height, and put a static sprite behind the chat window": "Зменшити висоту чату та розмістити статичний спрайт за вікном чату", "Waifu Mode": "Режим візуальної новели", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "Завжди показувати повний список контекстних дій для повідомлень чату, а не приховувати їх за '...' ", "Auto-Expand Message Actions": "Автоматично розгортати дії повідомлень", "Alternative UI for numeric sampling parameters with fewer steps": "Альтернативний інтерфейс для числових параметрів вибірки з меншою кількістю кроків", "Zen Sliders": "Слайдери зен", "Entirely unrestrict all numeric sampling parameters": "Повністю скасовує обмеження всіх числових параметрів вибірки", "Mad Lab Mode": "Режим лабораторії", "Time the AI's message generation, and show the duration in the chat log": "Вимірювати час генерації повідомлення ШІ та показувати тривалість у журналі чату", "Message Timer": "Таймер повідомлень", "Show a timestamp for each message in the chat log": "Показувати відмітку часу для кожного повідомлення у журналі чату", "Chat Timestamps": "Відмітки часу в чаті", "Show an icon for the API that generated the message": "Показувати значок для API, який згенерував повідомлення", "Model Icon": "Іконка моделі", "Show sequential message numbers in the chat log": "Показувати послідовні номери повідомлень у журналі чату", "Message IDs": "Ідентифікатори повідомлень", "Hide avatars in chat messages.": "Приховати аватари в повідомленнях чату.", "Hide Chat Avatars": "Приховати аватари чату", "Show the number of tokens in each message in the chat log": "Показувати кількість токенів у кожному повідомленні у журналі чату", "Show Message Token Count": "Кількість токенів у повідомленні", "Single-row message input area. Mobile only, no effect on PC": "Область введення повідомлення з одним рядком. Тільки для мобільних пристроїв, не впливає на ПК", "Compact Input Area (Mobile)": "Компактна область введення", "In the Character Management panel, show quick selection buttons for favorited characters": "У панелі Управління персонажами показувати кнопки швидкого вибору для улюблених персонажів", "Characters Hotswap": "Швидка заміна персонажів", "Enable magnification for zoomed avatar display.": "Увімкніть збільшення для збільшеного відображення аватара.", "Avatar Hover Magnification": "Збільшення аватара при наведенні", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "Вмикає ефект збільшення під час наведення курсора, коли ви відображаєте збільшений аватар після натискання зображення аватара в чаті.", "Show tagged character folders in the character list": "Показувати папки персонажів з тегами у списку персонажів", "Tags as Folders": "Теги як теки", "Tags_as_Folders_desc": "Останні зміни: теги мають бути позначені як папки в меню керування тегами, щоб вони відображалися як такі. Натисніть тут, щоб відкрити його.", "Character Handling": "Обробка персонажа", "If set in the advanced character definitions, this field will be displayed in the characters list.": "Якщо встановлено у розширених визначеннях персонажа, це поле буде відображено в списку персонажів.", "Char List Subheader": "Підзаголовок списку символів", "Character Version": "Версія персонажа", "Created by": "Ім'я автора", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "Використовувати нечітку відповідность та шукати персонажів у списку за всіма полями даних, а не лише за рядком імені", "Advanced Character Search": "Розширений пошук персонажа", "If checked and the character card contains a prompt override (System Prompt), use that instead": "Якщо відмічено і картка персонажа містить заміну запиту (Системний запит), використовувати її замість цього", "Prefer Character Card Prompt": "Перевага запиту персонажа", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "Якщо відмічено і картка персонажа містить заміну джейлбрейку (Інструкцію), використовуйте її замість цього", "Prefer Character Card Jailbreak": "Перевага джейлбрейку персонажа", "never_resize_avatars_tooltip": "Уникайте обрізання та зміни розміру імпортованих зображень символів. Коли вимкнено, обрізати/змінити розмір до 512x768.", "Never resize avatars": "Ніколи не змінювати розмір аватарів", "Show actual file names on the disk, in the characters list display only": "Показувати фактичні назви файлів на диску, тільки у відображенні списку персонажів", "Show avatar filenames": "Показувати імена файлів аватарів", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "Запитувати імпортувати вбудовані теги картки при імпорті персонажа. В іншому випадку вбудовані теги ігноруються", "Import Card Tags": "Імпортувати теги з картки", "Hide character definitions from the editor panel behind a spoiler button": "Ховати визначення персонажів з панелі редактора за кнопкою спойлера", "Spoiler Free Mode": "Режим без спойлерів", "Miscellaneous": "Різне", "Reload and redraw the currently open chat": "Перезавантажте та перетворіть на новий поточний чат", "Reload Chat": "Перезавантажити чат", "Debug Menu": "Меню налагодження", "Smooth Streaming": "Плавне потокове передавання", "Experimental feature. May not work for all backends.": "Експериментальна функція. Може працювати не для всіх серверних програм.", "Slow": "Повільно", "Fast": "швидко", "Play a sound when a message generation finishes": "Відтворювати звук, коли завершується генерація повідомлення", "Message Sound": "Звук повідомлення", "Only play a sound when ST's browser tab is unfocused": "Відтворювати звук лише тоді, коли вкладка браузера ST неактивна", "Background Sound Only": "Тільки фоновий звук", "Reduce the formatting requirements on API URLs": "Зменшити вимоги до форматування URL-адрес API", "Relaxed API URLS": "Послаблення URL-адреси API", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "Запитувати імпортувати інформацію про світ/книгу знань для кожного нового персонажа з вбудованою книгою. Якщо не відзначено, замість цього буде показано коротке повідомлення", "Lorebook Import Dialog": "Діалог імпорту книги знань", "Restore unsaved user input on page refresh": "Відновлювати незбережений введений користувачем текст при оновленні сторінки", "Restore User Input": "Відновлення введення користувача", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "Дозволяти переміщення деяких елементів інтерфейсу користувача, перетягуючи їх. Тільки для ПК, не впливає на мобільні пристрої", "Movable UI Panels": "Рухомі панелі", "MovingUI preset. Predefined/saved draggable positions": "Налаштування рухомого інтерфейсу. Передбачені/збережені позиції елементів, які можна перетягувати", "MUI Preset": "Попереднє встановлення MUI:", "Save movingUI changes to a new file": "Зберегти зміни в рухомому інтерфейсі у новий файл", "Reset MovingUI panel sizes/locations.": "Скинути розміри/розташування панелі MovingUI.", "Apply a custom CSS style to all of the ST GUI": "Застосовувати власний стиль CSS до всього графічного інтерфейсу ST", "Custom CSS": "Власний CSS", "Expand the editor": "Розгорніть редактор", "Chat/Message Handling": "Обробка чату/повідомлень", "# Messages to Load": "# Повідомлення Завантажити", "The number of chat history messages to load before pagination.": "Кількість повідомлень історії чату, які потрібно завантажити перед розбиттям на сторінки.", "(0 = All)": "(0 = усі)", "Streaming FPS": "Потоких оновлень на секунду", "Update speed of streamed text.": "Швидкість оновлення потокового тексту.", "Example Messages Behavior": "Поведінка прикладів повідомлень", "Gradual push-out": "Поступова заміна", "Always include examples": "Завжди включати приклади", "Never include examples": "Ніколи не включати приклади", "Send on Enter": "Відправити при натисканні Enter", "Disabled": "Вимкнено", "Automatic (PC)": "Автоматично (ПК)", "Press Send to continue": "Натисніть 'Відправити', щоб продовжити", "Show a button in the input area to ask the AI to continue (extend) its last message": "Показувати кнопку у області введення для запиту у ШІ про продовження (розширення) його останнього повідомлення", "Quick 'Continue' button": "Швидка кнопка 'Продовжити'", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "Показувати кнопки стрілок на останньому повідомленні в чаті, щоб згенерувати альтернативні відповіді ШІ. Як на ПК, так і на мобільних пристроях", "Swipes": "З<PERSON><PERSON><PERSON>и", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "Дозвольте використовувати жести перетягування на останньому повідомленні в чаті для запуску генерації змахів. Тільки для мобільних пристроїв, не впливає на ПК", "Gestures": "Жести", "Auto-load Last Chat": "Автоматичне завантаження останнього чату", "Auto-scroll Chat": "Автоматична прокрутка чату", "Save edits to messages without confirmation as you type": "Зберігати внесені зміни до повідомлень без підтвердження під час набору", "Auto-save Message Edits": "Автоматичне збереження редагувань повідомлень", "Confirm message deletion": "Підтвердити видалення повідомлення", "Auto-fix Markdown": "Автоматичне виправлення Markdown", "Disallow embedded media from other domains in chat messages": "Заборонити вбудовані мультимедійні дані з інших доменів у повідомленнях чату.", "Forbid External Media": "Заборонити зовнішні медіа", "Allow {{char}}: in bot messages": "Дозволити {{char}}: у повідомленнях бота", "Allow {{user}}: in bot messages": "Дозволити {{user}}: у повідомленнях бота", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "Пропускати кодування символів < та > у тексті повідомлення, дозволяючи підмножину розмітки HTML, а також Markdown", "Show tags in responses": "Показати <теги> в відповідях", "Allow AI messages in groups to contain lines spoken by other group members": "Дозволяти повідомленням ШІ у групах містити рядки, сказані іншими учасниками групи", "Relax message trim in Groups": "Послаблення обрізання повідомлень у групах", "Log prompts to console": "Записуйте запити у консоль", "Requests logprobs from the API for the Token Probabilities feature": "Запитувати імовірності з API для функції ймовірностей токенів", "Request token probabilities": "Запитувати ймовірності токенів", "Automatically reject and re-generate AI message based on configurable criteria": "Автоматично відхиляти та знову генерувати повідомлення ШІ на основі налаштованих критеріїв", "Auto-swipe": "Автоматичний змах", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "Вмикає функцію автоматичного змаху. Налаштування в цьому розділі діють лише тоді, коли увімкнено автоматичний змах", "Minimum generated message length": "Мінімальна довжина згенерованого повідомлення", "If the generated message is shorter than these many characters, trigger an auto-swipe": "Якщо згенероване повідомлення коротше за це, викликайте автоматичний змаху", "Blacklisted words": "Список заборонених слів", "words you dont want generated separated by comma ','": "слова, які ви не хочете генерувати, розділені комою ','", "Blacklisted word count to swipe": "Кількість заборонених слів для змаху", "Minimum number of blacklisted words detected to trigger an auto-swipe": "Мінімальна кількість заборонених слів, виявлених для запуску автоматичного змаху.", "AutoComplete Settings": "Налаштування автозаповнення", "Automatically hide details": "Автоматично приховувати деталі", "Determines how entries are found for autocomplete.": "Визначає спосіб пошуку записів для автозаповнення.", "Autocomplete Matching": "Зіставлення", "Starts with": "Починається з", "Includes": "Включає в себе", "Fuzzy": "нечіткий", "Sets the style of the autocomplete.": "Встановлює стиль автозаповнення.", "Autocomplete Style": "Стиль", "Follow Theme": "Слідкуйте за темою", "Dark": "Темний", "Sets the font size of the autocomplete.": "Встановлює розмір шрифту автозаповнення.", "Sets the width of the autocomplete.": "Встановлює ширину автозаповнення.", "Autocomplete Width": "Ши<PERSON><PERSON><PERSON>", "chat input box": "поле введення чату", "entire chat width": "вся ширина чату", "full window width": "повна ширина вікна", "STscript Settings": "Налаштування STscript", "Sets default flags for the STscript parser.": "Встановлює позначки за замовчуванням для аналізатора STscript.", "Parser Flags": "Прапори аналізатора", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "Перейдіть на суворіше екранування, дозволяючи екранувати всі розмежувальні символи зворотною похилою рискою, а також зворотні похилі риски.", "STRICT_ESCAPING": "STRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "Замініть усі макроси {{getvar::}} і {{getglobalvar::}} на змінні з областю видимості, щоб уникнути подвійної підстановки макросів.", "REPLACE_GETVAR": "REPLACE_GETVAR", "Change Background Image": "Змінити фонове зображення", "Filter": "Фільтр", "Automatically select a background based on the chat context": "Автоматичний вибір фону на основі контексту чату", "Auto-select": "Автов<PERSON>б<PERSON>р", "System Backgrounds": "Системні фони", "Chat Backgrounds": "Фони чату", "bg_chat_hint_1": "Фонове зображення чату, створене за допомогою", "bg_chat_hint_2": "тут з’явиться розширення.", "Extensions": "Розширення", "Notify on extension updates": "Повідомити про оновлення розширень", "Manage extensions": "Керувати розширеннями", "Import Extension From Git Repo": "Імпортувати розширення з репозиторію Git", "Install extension": "Встановити розширення", "Extras API:": "Додатковий API:", "Auto-connect": "Автоматичне підключення", "Extras API URL": "Додаткова URL-адреса API", "Extras API key (optional)": "Додатковий ключ API (необов'язково)", "Persona Management": "Управління персонами", "How do I use this?": "Як це використовувати?", "Click for stats!": "Клацніть для статистики!", "Usage Stats": "Статистика використання", "Backup your personas to a file": "Зробіть резервну копію своїх персон у файл", "Backup": "Резервне копіювання", "Restore your personas from a file": "Відновіть свої персони з файлу", "Restore": "Відновлення", "Create a dummy persona": "Створити порожню персону", "Create": "Створити", "Toggle grid view": "Перемкнути вид сітки", "No persona description": "[Без опису]", "Name": "Ім'я", "Enter your name": "Введіть своє ім'я", "Click to set a new User Name": "Клацніть, щоб встановити нове ім'я користувача", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "Клацніть, щоб заблокувати обрану персону в поточному чаті. Клацніть ще раз, щоб видалити блокування.", "Click to set user name for all messages": "Клацніть, щоб встановити ім'я користувача для всіх повідомлень", "Persona Description": "Опис персони", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "Приклад: [{{user}} - 28-річна румунська дівчина-кішка].", "Tokens persona description": "Опис персонажа", "Position:": "Позиція:", "In Story String / Prompt Manager": "У рядку історії / Менеджері запитів", "Top of Author's Note": "На вершині примітки автора", "Bottom of Author's Note": "Унизу примітки автора", "In-chat @ Depth": "In-chat @ Depth", "Depth:": "Глибина:", "Role:": "роль:", "System": "система", "User": "Користув<PERSON><PERSON>", "Assistant": "помічник", "Show notifications on switching personas": "Показувати сповіщення при зміні персон", "Character Management": "Управління персонажем", "Locked = Character Management panel will stay open": "Заблоковано = Панель управління персонажем залишиться відкритою", "Select/Create Characters": "Вибрати/створити персонаж<PERSON>в", "Favorite characters to add them to HotSwaps": "Оберіть улюблених персонажів, щоб додати їх до HotSwaps", "Token counts may be inaccurate and provided just for reference.": "Лічильники токенів можуть бути неточними і надаються тільки для довідки.", "Total tokens": "Всього жетонів", "Calculating...": "Розрахунок...", "Tokens": "Токени", "Permanent tokens": "Постійні жетони", "Permanent": "Постійний", "About Token 'Limits'": "Про «Обмеження» токенів", "Toggle character info panel": "Перемкнути панель інформації про персонажа", "Name this character": "Дайте цьому персонажу ім'я", "extension_token_counter": "Жетони:", "Click to select a new avatar for this character": "Клацніть, щоб вибрати новий аватар для цього персонажа", "Add to Favorites": "Додати до обраного", "Advanced Definition": "Розширені визначення", "Character Lore": "Книга знань персонажа", "Chat Lore": "Історія чату", "Export and Download": "Експорт і завантаження", "Duplicate Character": "Дублювати персонажа", "Create Character": "Створити персонажа", "Delete Character": "Видалити персонажа", "More...": "Більше...", "Link to World Info": "Зв'язати з інформацією про світ", "Import Card Lore": "Імпортувати книгу знань з картки", "Scenario Override": "Перевизначити сценарій", "Convert to Persona": "Перетворити в Persona", "Rename": "Перейменувати", "Link to Source": "Посилання на джерело", "Replace / Update": "Замінити / Оновити", "Import Tags": "Імпорт тегів", "Search / Create Tags": "Пошук / Створення тегів", "View all tags": "Переглянути всі теги", "Creator's Notes": "Нотатки творця", "Show / Hide Description and First Message": "Показати / приховати опис і перше повідомлення", "Character Description": "Опис персонажа", "Click to allow/forbid the use of external media for this character.": "Натисніть, щоб дозволити/заборонити використання зовнішнього носія для цього персонажа.", "Ext. Media": "доп. ЗМІ", "Describe your character's physical and mental traits here.": "Опишіть фізичні та психічні риси вашого персонажа тут.", "First message": "Перше повідомлення", "Click to set additional greeting messages": "Клацніть, щоб встановити додаткові привітальні повідомлення", "Alt. Greetings": "Альт. вітаю", "This will be the first message from the character that starts every chat.": "Це буде перше повідомлення від персонажа, яке починає кожен чат.", "Group Controls": "Керування групою", "Chat Name (Optional)": "Назва чату (необов'язково)", "Click to select a new avatar for this group": "Клацніть, щоб вибрати новий аватар для цієї групи", "Group reply strategy": "Стратегія відповіді у групі", "Natural order": "Звичайний порядок", "List order": "Порядок списку", "Group generation handling mode": "Режим обробки групового формування", "Swap character cards": "Поміняйтеся картками персонаж<PERSON>в", "Join character cards (exclude muted)": "Приєднатися до карток персонажів (за винятком тихих)", "Join character cards (include muted)": "Приєднатися до карток персонажів (включно з приглушеними)", "Inserted before each part of the joined fields.": "Вставляється перед кожною частиною об’єднаних полів.", "Join Prefix": "Префікс приєднання", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "Якщо вибрано «Об’єднати картки персонажів», усі відповідні поля персонажів об’єднуються.\rЦе означає, що, наприклад, у рядку історії всі описи персонажів будуть об’єднані в один великий текст.\rЯкщо ви хочете, щоб ці поля були розділені, ви можете визначити тут префікс або суфікс.\r\rЦе значення підтримує звичайні макроси, а також замінює {{char}} на ім’я відповідного символу, а <FIELDNAME> — на назву частини (наприклад, опис, особистість, сценарій тощо).", "Inserted after each part of the joined fields.": "Вставляється після кожної частини об’єднаних полів.", "Join Suffix": "Суфікс приєднання", "Set a group chat scenario": "Встановити сценарій групового чату", "Click to allow/forbid the use of external media for this group.": "Натисніть, щоб дозволити/заборонити використання зовнішніх носіїв для цієї групи.", "Restore collage avatar": "Відновити аватар колажу", "Allow self responses": "Дозволити відповіді самому собі", "Auto Mode": "Автоматичний режим", "Auto Mode delay": "Затримка автоматичного режиму", "Hide Muted Member Sprites": "Приховати вимкнені спрайти учасників", "Current Members": "Поточні учасники", "Add Members": "Додати учасників", "Create New Character": "Створити нового персонажа", "Import Character from File": "Імпортувати персонажа з файлу", "Import content from external URL": "Імпортувати вміст з зовнішнього URL", "Create New Chat Group": "Створити нову групу чату", "Characters sorting order": "Порядок сортування персонажів", "A-Z": "А-Я", "Z-A": "Я-А", "Newest": "Найновіші", "Oldest": "Найст<PERSON><PERSON><PERSON><PERSON>і", "Favorites": "Виб<PERSON>а<PERSON><PERSON>", "Recent": "Останн<PERSON>", "Most chats": "Більше чатів", "Least chats": "Мен<PERSON>е чатів", "Most tokens": "Найбільше токенів", "Least tokens": "Найменше токенів", "Random": "Випадковий", "Toggle character grid view": "Перемкнути вид сітки персонажів", "Bulk_edit_characters": "Масове редагування персонажів", "Bulk select all characters": "Масове виділення всіх символів", "Bulk delete characters": "Масове видалення персонажів", "popup-button-save": "зберегти", "popup-button-yes": "Так", "popup-button-no": "Немає", "popup-button-cancel": "Скасувати", "popup-button-import": "Імпорт", "Advanced Definitions": "Розширені визначення", "Prompt Overrides": "Перевизначення підказок", "(For Chat Completion and Instruct Mode)": "(Для завершення чату та режиму інструктажу)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "Вставте {{original}} в будь-яке поле, щоб включити відповідний запит з налаштувань системи.", "Main Prompt": "Головний запит", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "Будь-який вміст тут замінить типову головний запит, використовуваний для цього персонажа. (v2 специфікація: system_prompt)", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "Будь-який вміст тут замінить запит для джейлбрейку, використовуваний для цього персонажа. (v2 специфікація: post_history_instructions)", "Creator's Metadata (Not sent with the AI prompt)": "Метадані творця (не надсилаються до ШІ)", "Creator's Metadata": "Метадані творця", "(Not sent with the AI Prompt)": "(Не надсилається разом із запитом AI)", "Everything here is optional": "Все тут є необов'язковим", "(Botmaker's name / Contact Info)": "(Ім'я розробника бота / Контактна інформація)", "(If you want to track character versions)": "(Якщо ви хочете відстежувати версії персонажа)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(Опишіть бота, дайте поради щодо використання або перерахуйте моделі чату, на яких він був протестований. Це буде відображатися у списку персонажів.)", "Tags to Embed": "Теги для вбудовування", "(Write a comma-separated list of tags)": "(Напишіть список тегів, розділених комами)", "Personality summary": "О<PERSON>ис особистості", "(A brief description of the personality)": "(Короткий опис особистості)", "Scenario": "Сценарій", "(Circumstances and context of the interaction)": "(Обставини та контекст взаємодії)", "Character's Note": "Примітка персонажа", "(Text to be inserted in-chat @ designated depth and role)": "(Текст, який потрібно вставити в чат @ визначена глибина та роль)", "@ Depth": "@ Глибина", "Role": "Роль", "Talkativeness": "Балак<PERSON>чість", "How often the character speaks in group chats!": "Як часто персонаж розмовляє в групових чатах!", "How often the character speaks in": "Як часто персонаж говорить", "group chats!": "групових чатах!", "Shy": "Сором'язливий", "Normal": "Нормальний", "Chatty": "Балакучий", "Examples of dialogue": "Приклади діалогу", "Important to set the character's writing style.": "Важливо щоб встановити стиль письма персонажа.", "(Examples of chat dialog. Begin each example with START on a new line.)": "(Приклади діалогу в чаті. Почніть кожний приклад зі <START> на новому рядку.)", "Save": "Зберегти", "Chat History": "Історія чату", "Import Chat": "Імпорт чату", "Copy to system backgrounds": "Скопіювати в системні фони", "Rename background": "Перейменувати фон", "Lock": "Замок", "Unlock": "Розблокувати", "Delete background": "Видалити фон", "Chat Scenario Override": "Перевизначення сценарію чату", "Remove": "Видалити", "Type here...": "Введіть тут...", "Chat Lorebook": "Чат Lorebook для", "Chat Lorebook for": "Чат Lorebook для", "chat_world_template_txt": "Вибрану світову інформацію буде прив’язано до цього чату. Під час генерації відповіді AI,\n                    він буде поєднаний із записами з глобальних книг і книг персонажів.", "Select a World Info file for": "Виберіть файл інформації про світ для", "Primary Lorebook": "Основний збірник легенд", "A selected World Info will be bound to this character as its own Lorebook.": "Вибрана інформація про світ буде пов'язана з цим персонажем як власна книга знань.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "Під час генерації відповіді ШІ, вона буде поєднана з записами із глобального селектора інформації про світ.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "Експорт персонажа також експортуватиме вибраний файл збірника легенд, вбудований у дані JSON.", "Additional Lorebooks": "Додаткові книги знань", "Associate one or more auxillary Lorebooks with this character.": "Асоціюйте одну або кілька допоміжних книг знань з цим персонажем.", "NOTE: These choices are optional and won't be preserved on character export!": "ПРИМІТКА: Ці вибори є необов'язковими і не будуть збережені при експорті персонажа!", "Rename chat file": "Перейменувати файл чату", "Export JSONL chat file": "Експортувати файл чату у форматі JSONL", "Download chat as plain text document": "Завантажити чат як документ у форматі простого тексту", "Delete chat file": "Видалити файл чату", "Use tag as folder": "Позначити як папку", "Hide on character card": "Сховати на картці персонажа", "Delete tag": "Видалити тег", "Entry Title/Memo": "Заголовок запису", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "Статус вступу до WI:\r🔵 Постійно\r🟢 Нормально\r🔗 Векторизовано\r❌ Вимкнено", "WI_Entry_Status_Constant": "Постійний", "WI_Entry_Status_Normal": "нормальний", "WI_Entry_Status_Vectorized": "Векторизований", "WI_Entry_Status_Disabled": "Вимкнено", "T_Position": "↑Символ: перед визначеннями персонажу\n↓Символ: після визначень персонажу\n↑AN: перед авторськими примітками\n↓AN: після авторських приміток\n@D: на глибині", "Before Char Defs": "↑Визначення персонажу", "After Char Defs": "↓Визначення персонажу", "Before EM": "↑EM", "After EM": "↓EM", "Before AN": "↑AN", "After AN": "↓AN", "at Depth System": "@D ⚙️", "at Depth User": "@D 👤", "at Depth AI": "@D 🤖", "Depth": "Гли<PERSON><PERSON>на", "Order:": "Порядок:", "Order": "Порядок:", "Trigger %:": "Тригер %:", "Probability": "Ймовірність", "Duplicate world info entry": "Подвійний запис інформації про світ", "Delete world info entry": "Видалити запис інформації про світ", "Comma separated (required)": "Розділення комами (обов'язково)", "Primary Keywords": "Основні ключові слова", "Keywords or Regexes": "Ключові слова або регулярні вирази", "Comma separated list": "Список, розділений комами", "Switch to plaintext mode": "Перейти в режим відкритого тексту", "Logic": "Логіка", "AND ANY": "І БУДЬ-ЯКІ", "AND ALL": "І ВСІ", "NOT ALL": "НЕ ВСІ", "NOT ANY": "НЕ БУДЬ-ЯКІ", "(ignored if empty)": "(ігнорується, якщо порожній)", "Optional Filter": "Додатковий фільтр", "Keywords or Regexes (ignored if empty)": "Ключові слова або регулярні вирази (ігноруються, якщо пусті)", "Comma separated list (ignored if empty)": "Список, розділений комами (ігнорується, якщо порожній)", "Use global setting": "Використовувати глобальні налаштування", "Case-Sensitive": "З урахуванням регістру", "Yes": "Так", "No": "Ні", "Can be used to automatically activate Quick Replies": "Можна використовувати для автоматичної активації швидких відповідей", "Automation ID": "Ідентифіка<PERSON>ор автоматизац<PERSON>ї", "( None )": "( Жодного )", "Content": "Зміст", "Exclude from recursion": "Виключити з рекурсії", "Prevent further recursion (this entry will not activate others)": "Запобігти подальшій рекурсії (цей запис не активує інші)", "Delay until recursion (this entry can only be activated on recursive checking)": "Затримка до рекурсії (цей запис можна активувати лише під час рекурсивної перевірки)", "What this keyword should mean to the AI, sent verbatim": "Що це ключове слово повинно означати для ШІ, надсилається дослівно", "Filter to Character(s)": "Фільтр для персонажів", "Character Exclusion": "Виключення персонажів", "-- Characters not found --": "-- Пер<PERSON><PERSON><PERSON><PERSON><PERSON> не знайдені --", "Inclusion Group": "Група включення", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "Групи включення гарантують, що одночасно активується лише один запис із групи, якщо їх активовано декілька.\rПідтримує кілька груп, розділених комами.\r\rДокументація: World Info - Inclusion Group", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "Пріоритезувати цей запис: якщо позначено, цей запис має пріоритет серед усіх вибраних.\rЯкщо кілька пріоритетів, вибирається той, який має найвищий «порядок».", "Only one entry with the same label will be activated": "Буде активовано лише один запис з однією міткою", "A relative likelihood of entry activation within the group": "Відносна ймовірність активації входу в групу", "Group Weight": "Вага групи", "Selective": "Вибірковий", "Use Probability": "Використовувати ймовірність", "Add Memo": "Додати нагадування", "Text or token ids": "Текст або [ідентифікатори маркерів]", "close": "закрити", "prompt_manager_edit": "Редагувати", "prompt_manager_name": "Ім'я", "A name for this prompt.": "Назва цього підказки.", "To whom this message will be attributed.": "Кому буде віднесено це повідомлення.", "AI Assistant": "ШІ помічник", "prompt_manager_position": "Позиція", "Next to other prompts (relative) or in-chat (absolute).": "Поруч з іншими підказками (відносні) або в чаті (абсолютні).", "prompt_manager_relative": "Відносна", "prompt_manager_depth": "Гли<PERSON><PERSON>на", "0 = after the last message, 1 = before the last message, etc.": "0 = після останнього повідомлення, 1 = перед останнім повідомленням тощо.", "Prompt": "Запит", "The prompt to be sent.": "Підказка для надсилання.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "Це підказка не може бути перевизначено картками символів, навіть якщо перевизначення є кращим.", "prompt_manager_forbid_overrides": "Заборонити перевизначення", "reset": "скинути", "save": "зберегти", "This message is invisible for the AI": "Це повідомлення невидиме для ШІ", "Message Actions": "Дії з повідомленнями", "Translate message": "Перекласти повідомлення", "Generate Image": "Створити зображення", "Narrate": "Розповідати", "Exclude message from prompts": "Виключити повідомлення з підказок", "Include message in prompts": "Включити повідомлення в підказки", "Embed file or image": "Вбудувати файл або зображення", "Create checkpoint": "Створити контрольну точку", "Create Branch": "Створити гілку", "Copy": "Копіювати", "Open checkpoint chat": "Відкрити чат КПП", "Edit": "Редагувати", "Confirm": "Підтвердити", "Copy this message": "Скопіювати це повідомлення", "Delete this message": "Видалити це повідомлення", "Move message up": "Перемістити повідомлення вгору", "Move message down": "Перемістити повідомлення вниз", "Enlarge": "Зб<PERSON>льшити", "Welcome to SillyTavern!": "Ласкаво просимо до SillyTavern!", "welcome_message_part_1": "Читати", "welcome_message_part_2": "Оф<PERSON><PERSON><PERSON>йна документація", "welcome_message_part_3": null, "welcome_message_part_4": "Тип", "welcome_message_part_5": "у чаті для команд і макросів.", "welcome_message_part_6": "Приєднуйтесь до", "Discord server": "<PERSON>ер<PERSON><PERSON><PERSON>rd", "welcome_message_part_7": "для інформації та оголошень.", "SillyTavern is aimed at advanced users.": "SillyTavern орієнтована на досвідчених користувачів.", "If you're new to this, enable the simplified UI mode below.": "Якщо ви новачок у цьому, увімкніть спрощений режим інтерфейсу користувача нижче.", "Change it later in the 'User Settings' panel.": "Змініть це пізніше на панелі «Налаштування користувача».", "Enable simple UI mode": "Увімкніть простий режим інтерфейсу користувача", "Looking for AI characters?": "Шукаєте персонажів зі штучним інтелектом?", "onboarding_import": "Імпорт", "from supported sources or view": "із підтримуваних джерел або перегляду", "Sample characters": "Зразок символів", "Your Persona": "Ваша персона", "Before you get started, you must select a persona name.": "Перш ніж почати, ви повинні вибрати ім'я особи.", "welcome_message_part_8": "Це можна будь-коли змінити за допомогою", "welcome_message_part_9": "значок.", "Persona Name:": "Ім'я особи:", "Temporarily disable automatic replies from this character": "Тимчасово вимкнути автоматичні відповіді від цього персонажа", "Enable automatic replies from this character": "Увімкнути автоматичні відповіді від цього персонажа", "Trigger a message from this character": "Викликати повідомлення від цього персонажа", "Move up": "Перемістити вгору", "Move down": "Перемістити вниз", "View character card": "Переглянути картку персонажа", "Remove from group": "Вилучити з групи", "Add to group": "Додати до групи", "Alternate Greetings": "Альтернативні привітання", "Alternate_Greetings_desc": "Вони відображатимуться як гортання по першому повідомленню під час початку нового чату.\n                Учасники групи можуть вибрати одного з них, щоб розпочати розмову.", "Alternate Greetings Hint": "Натисніть кнопку, щоб почати!", "(This will be the first message from the character that starts every chat)": "(Це буде перше повідомлення від персонажа, яке починає кожен чат)", "Forbid Media Override explanation": "Можливість поточного персонажа/групи використовувати зовнішні носії в чатах.", "Forbid Media Override subtitle": "Медіа: зоб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, відео, ауді<PERSON>. Зовнішній: не розміщений на локальному сервері.", "Always forbidden": "Завжди заборонено", "Always allowed": "Завжди дозволено", "View contents": "Переглянути вміст", "Remove the file": "Видаліть файл", "Unique to this chat": "Унікальний для цього чату", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "Контрольні точки успадковують Примітку від свого батька і можуть бути змінені окремо після цього.", "Include in World Info Scanning": "Включити в World Info Scanning", "Before Main Prompt / Story String": "Перед головною підказкою / рядком історії", "After Main Prompt / Story String": "Після основного підказки / рядка історії", "as": "як", "Insertion Frequency": "Частота введення", "(0 = Disable, 1 = Always)": "(0 = вимкнено, 1 = завжди)", "User inputs until next insertion:": "Введення користувача до наступного вставлення:", "Character Author's Note (Private)": "Примітка автора персонажа (приватна)", "Won't be shared with the character card on export.": "Не буде надано спільний доступ до картки персонажа під час експорту.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "Буде автоматично додано як примітку автора для цього персонажа. Буде використовуватися в групах, але\n                            не можна змінити, коли відкрито груповий чат.", "Use character author's note": "Використовуйте примітку автора персонажа", "Replace Author's Note": "Замінити примітку автора", "Default Author's Note": "Примітка автора за замовчуванням", "Will be automatically added as the Author's Note for all new chats.": "Буде автоматично додано як примітку автора для всіх нових чатів.", "Chat CFG": "Чат CFG", "1 = disabled": "1 = вимкнено", "write short replies, write replies using past tense": "писати короткі відповіді, писати відповіді, використовуючи минулий час", "Positive Prompt": "Позитивна підказка", "Use character CFG scales": "Використовуйте шкали CFG символів", "Character CFG": "CFG символів", "Will be automatically added as the CFG for this character.": "Буде автоматично додано як CFG для цього персонажа.", "Global CFG": "Глобальна CFG", "Will be used as the default CFG options for every chat unless overridden.": "Використовуватиметься як параметри CFG за замовчуванням для кожного чату, якщо не буде замінено.", "CFG Prompt Cascading": "CFG Prompt Cascading", "Combine positive/negative prompts from other boxes.": "Поєднайте позитивні/негативні підказки з інших ящиків.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "Наприклад, відмічаючи поля чату, глобального та символьного, усі негативні підказки об’єднуються в рядок, розділений комами.", "Always Include": "Завжди включати", "Chat Negatives": "Негативи чату", "Character Negatives": "Негативні символи", "Global Negatives": "Глобальні негативи", "Custom Separator:": "Спеціальний роздільник:", "Insertion Depth:": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> вставки:", "Token Probabilities": "Ймовірності токенів", "Select a token to see alternatives considered by the AI.": "Виберіть маркер, щоб побачити альтернативи, розглянуті ШІ.", "Not connected to API!": "Не підключено до API!", "Type a message, or /? for help": "Введіть повідомлення або /? для допомоги", "Continue script execution": "Продовжити виконання сценарію", "Pause script execution": "Призупинити виконання сценарію", "Abort script execution": "Перервати виконання сценарію", "Abort request": "Скасувати запит", "Continue the last message": "Продовжити останнє повідомлення", "Send a message": "Надіслати повідомлення", "Close chat": "Закрити чат", "Toggle Panels": "Перемикати панелі", "Back to parent chat": "Повернутися до головного чату", "Save checkpoint": "Зберегти КПП", "Convert to group": "Перетворити на групу", "Start new chat": "Розпочати новий чат", "Manage chat files": "Керувати файлами чату", "Delete messages": "Видалити повідомлення", "Regenerate": "Регенерувати", "Ask AI to write your message for you": "Попросити ШІ написати ваше повідомлення за вас", "Impersonate": "Перевтілення", "Continue": "Продовжити", "Bind user name to that avatar": "Прив'язати ім'я користувача до цього аватара", "Change persona image": "Змінити зображення особистості", "Select this as default persona for the new chats.": "Виберіть це як типову персону для нових чатів.", "Delete persona": "Видалити персону", "These characters are the winners of character design contests and have outstandable quality.": "Ці персонажі є переможцями конкурсів дизайну персонажів і мають надзвичайну якість.", "Contest Winners": "Переможці конкурсу", "These characters are the finalists of character design contests and have remarkable quality.": "Ці персонажі є фіналістами конкурсів дизайну персонажів і мають надзвичайну якість.", "Featured Characters": "Вибрані персонажі", "Attach a File": "Прикріпити файл", "Open Data Bank": "Відкритий банк даних", "Enter a URL or the ID of a Fandom wiki page to scrape:": "Введіть URL-адресу або ідентифікатор вікі-сторінки Fandom, яку потрібно отримати:", "Examples:": "приклади:", "Example:": "приклад:", "Single file": "Один файл", "All articles will be concatenated into a single file.": "Усі статті будуть об’єднані в один файл.", "File per article": "Файл на статтю", "Each article will be saved as a separate file.": "Не рекомендовано. Кожна стаття буде збережена як окремий файл.", "Data Bank": "Банк даних", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "Ці файли будуть доступні для розширень, які підтримують вкладення (наприклад, Vector Storage).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "Підтримувані типи файлів: звичайний текст, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "Перетягніть файли сюди, щоб завантажити.", "Date (Newest First)": "Дата (Спочатку найновіші)", "Date (Oldest First)": "Дата (спочатку найдавніші)", "Name (A-Z)": "Ім'я (А-Я)", "Name (Z-A)": "Ім'я (Я-А)", "Size (Smallest First)": "Розмір (спочатку найменший)", "Size (Largest First)": "Розмір (спочатку найбільший)", "Bulk Edit": "Масове редагування", "Select All": "Вибрати все", "Select None": "Виберіть Немає", "Global Attachments": "Глобальні вкладення", "These files are available for all characters in all chats.": "Ці файли доступні для всіх персонажів у всіх чатах.", "Character Attachments": "Вкладення символів", "These files are available the current character in all chats they are in.": "Ці файли доступні поточному персонажу в усіх чатах, у яких він перебуває.", "Saved locally. Not exported.": "Збережено локально. Не експортовано.", "Chat Attachments": "Вкладення чату", "These files are available to all characters in the current chat.": "Ці файли доступні для всіх персонажів поточного чату.", "Enter a base URL of the MediaWiki to scrape.": "Введіть базову URL-адресу MediaWiki, яку потрібно отримати.", "Don't include the page name!": "Не вказуйте назву сторінки!", "Enter web URLs to scrape (one per line):": "Введіть URL-адреси веб-сторінок для сканування (по одній на рядок):", "Enter a video URL to download its transcript.": "Введіть URL-адресу або ідентифікатор відео, щоб завантажити його текст.", "Expression API": "Місцевий\nДодатково\nмагістр права", "ext_sum_with": "Підсумуйте за допомогою:", "ext_sum_main_api": "Основний API", "ext_sum_current_summary": "Поточне резюме:", "ext_sum_restore_previous": "Відновити попередній", "ext_sum_memory_placeholder": "Зведення буде створено тут...", "Trigger a summary update right now.": "Підведіть підсумки зараз", "ext_sum_force_text": "Підведіть підсумки зараз", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "Вимкніть автоматичне оновлення підсумків. Під час паузи підсумок залишається таким, як є. Ви все ще можете примусово оновити, натиснувши кнопку «Підсумувати зараз» (яка доступна лише в основному API).", "ext_sum_pause": "Пауза", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "Пропустіть інформацію про світ і примітку автора з тексту, який потрібно підсумувати. Діє лише під час використання основного API. Extras API завжди пропускає WI/AN.", "ext_sum_no_wi_an": "Немає WI/AN", "ext_sum_settings_tip": "Редагувати підказку підсумовування, позицію вставки тощо.", "ext_sum_settings": "Підсумкові налаштування", "ext_sum_prompt_builder": "Оперативний забудовник", "ext_sum_prompt_builder_1_desc": "Розширення створить власну підказку, використовуючи повідомлення, які ще не були підсумовані. Блокує чат, доки не буде згенеровано підсумок.", "ext_sum_prompt_builder_1": "Сирий, блокуючий", "ext_sum_prompt_builder_2_desc": "Розширення створить власну підказку, використовуючи повідомлення, які ще не були підсумовані. Не блокує чат під час генерації зведення. Не всі серверні модулі підтримують цей режим.", "ext_sum_prompt_builder_2": "Необроблений, неблокуючий", "ext_sum_prompt_builder_3_desc": "Розширення використовуватиме звичайний конструктор головних підказок і додасть до нього підсумковий запит як останнє системне повідомлення.", "ext_sum_prompt_builder_3": "Класичний, блокуючий", "Summary Prompt": "Резюме", "ext_sum_restore_default_prompt_tip": "Відновити запит за замовчуванням", "ext_sum_prompt_placeholder": "Це підказка буде надіслано ШІ для запиту створення підсумку. {{words}} перетворює на параметр «Кількість слів».", "ext_sum_target_length_1": "Цільова довжина резюме", "ext_sum_target_length_2": null, "ext_sum_target_length_3": "слова)", "ext_sum_api_response_length_1": "<PERSON><PERSON><PERSON><PERSON>на відповіді API", "ext_sum_api_response_length_2": null, "ext_sum_api_response_length_3": "жетони)", "ext_sum_0_default": "0 = за замовчуванням", "ext_sum_raw_max_msg": "[Raw] Максима<PERSON>ьна кількість повідомлень на запит", "ext_sum_0_unlimited": "0 = необмежений", "Update frequency": "Частота оновлення", "ext_sum_update_every_messages_1": "Оновлювати кожен", "ext_sum_update_every_messages_2": "повідомлення", "ext_sum_0_disable": "0 = вимкнено", "ext_sum_auto_adjust_desc": "Спробуйте автоматично налаштувати інтервал на основі показників чату.", "ext_sum_update_every_words_1": "Оновлювати кожен", "ext_sum_update_every_words_2": "слова", "ext_sum_both_sliders": "Якщо обидва повзунки відрізняються від нуля, обидва запускатимуть підсумкові оновлення через відповідні інтервали.", "ext_sum_injection_template": "Шаблон ін'єкції", "ext_sum_memory_template_placeholder": "{{summary}} перейде до поточного вмісту резюме.", "ext_sum_injection_position": "Позиція ін'єкції", "How many messages before the current end of the chat.": "Скільки повідомлень до поточного завершення чату.", "ext_regex_title": "Регулярний вираз", "ext_regex_new_global_script": "+ Глоб<PERSON>л<PERSON>ний", "ext_regex_new_scoped_script": "+ Огляд", "ext_regex_import_script": "Імпорт", "ext_regex_global_scripts": "Глобальні сценарії", "ext_regex_global_scripts_desc": "Доступно для всіх персонажів. Збережено в локальних налаштуваннях.", "ext_regex_scoped_scripts": "Охоплені сценарії", "ext_regex_scoped_scripts_desc": "Доступно лише для цього персонажа. Збережено в даних картки.", "Regex Editor": "Редактор регулярних виразів", "Test Mode": "Тестовий режим", "ext_regex_desc": "Regex — це інструмент для пошуку/заміни рядків за допомогою регулярних виразів. Якщо ви хочете дізнатися більше, натисніть на ? поруч із заголовком.", "Input": "Введення", "ext_regex_test_input_placeholder": "Друкуйте тут...", "Output": "<PERSON>и<PERSON><PERSON>д", "ext_regex_output_placeholder": "Порожній", "Script Name": "Назва сценарію", "Find Regex": "Знайдіть Regex", "Replace With": "Замінити", "ext_regex_replace_string_placeholder": "Використовуйте {{match}}, щоб включити відповідний текст із Find Regex або $1, $2 тощо для груп захоплення.", "Trim Out": "Обрізати", "ext_regex_trim_placeholder": "Глобально видаляє будь-які небажані частини зі збігу регулярного виразу перед заміною. Відокремте кожен елемент за допомогою enter.", "ext_regex_affects": "Впливає", "ext_regex_user_input": "Введення користувача", "ext_regex_ai_output": "<PERSON>их<PERSON>д <PERSON>", "Slash Commands": "Слеш команди", "ext_regex_min_depth_desc": "Якщо застосувати до підказок або відображення, впливати лише на повідомлення, що містять принаймні N рівнів. 0 = останнє повідомлення, 1 = передостаннє повідомлення тощо. Враховуються лише записи WI @Depth і придатні для використання повідомлення, тобто не приховані чи системні.", "Min Depth": "Мінімальна глибина", "ext_regex_min_depth_placeholder": "Необмежений", "ext_regex_max_depth_desc": "Якщо застосувати до підказок або відображення, впливати лише на повідомлення не більше N рівнів. 0 = останнє повідомлення, 1 = передостаннє повідомлення тощо. Враховуються лише записи WI @Depth і придатні для використання повідомлення, тобто не приховані чи системні.", "ext_regex_other_options": "Інші параметри", "Only Format Display": "Лише форматувати дисплей", "ext_regex_only_format_prompt_desc": "Історія чату не зміниться, лише підказка під час надсилання запиту (під час створення).", "Only Format Prompt (?)": "Лише запит на форматування", "Run On Edit": "Запустіть на редагуванні", "ext_regex_substitute_regex_desc": "Замініть {{макроси}} у Find Regex перед його запуском", "Substitute Regex": "Замініть Regex", "ext_regex_import_target": "Імпорт до:", "ext_regex_disable_script": "Вимкнути скрипт", "ext_regex_enable_script": "Увімкнути сценарій", "ext_regex_edit_script": "Редагувати сценарій", "ext_regex_move_to_global": "Перейти до глобальних сценаріїв", "ext_regex_move_to_scoped": "Перейти до сценаріїв із обмеженою областю", "ext_regex_export_script": "Скрипт експорту", "ext_regex_delete_script": "Видалити сценарій", "Trigger Stable Diffusion": "Тригер стабільної дифузії", "sd_Yourself": "себе", "sd_Your_Face": "Твоє обличчя", "sd_Me": "я", "sd_The_Whole_Story": "Вся історія", "sd_The_Last_Message": "Останнє повідомлення", "sd_Raw_Last_Message": "Необроблене останнє повідомлення", "sd_Background": "Фон", "Image Generation": "Генерація зображень", "sd_refine_mode": "Дозвольте редагувати підказки вручну, перш ніж надсилати їх до API генерації", "sd_refine_mode_txt": "Редагувати підказки перед створенням", "sd_interactive_mode": "Автоматично створювати зображення під час надсилання повідомлень на зразок \"надішліть мені фотографію кота\".", "sd_interactive_mode_txt": "Інтерактивний режим", "sd_multimodal_captioning": "Використовуйте мультимодальні субтитри, щоб створювати підказки для портретів користувачів і персонажів на основі їхніх аватарів.", "sd_multimodal_captioning_txt": "Використовуйте мультимодальні субтитри для портретів", "sd_expand": "Автоматично розширюйте підказки за допомогою моделі генерації тексту", "sd_expand_txt": "Підказки автоматичного покращення", "sd_snap": "Запити на створення прив’язки з примусовим співвідношенням сторін (портрети, фон) до найближчої відомої роздільної здатності, намагаючись зберегти абсолютну кількість пікселів (рекомендовано для SDXL).", "sd_snap_txt": "Зніміть роздільну здатність із автоматичним налаштуванням", "Source": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sd_auto_url": "Приклад: {{auto_url}}", "Authentication (optional)": "Автентифікація (необов'язково)", "Example: username:password": "Приклад: ім'я користувача: пароль", "Important:": "Важливо:", "sd_auto_auth_warning_1": "запустіть SD Web UI за допомогою", "sd_auto_auth_warning_2": "прапор! Сервер має бути доступним із хост-машини SillyTavern.", "sd_drawthings_url": "Приклад: {{drawthings_url}}", "sd_drawthings_auth_txt": "запустіть програму DrawThings із увімкненим перемикачем HTTP API в інтерфейсі користувача! Сервер має бути доступним із хост-машини SillyTavern.", "sd_vlad_url": "Приклад: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "Сервер має бути доступним із хост-машини SillyTavern.", "Hint: Save an API key in AI Horde API settings to use it here.": "Підказка: збер<PERSON>жіть ключ API в налаштуваннях AI Horde API, щоб використовувати його тут.", "Allow NSFW images from Horde": "Дозволити зображення NSFW від Horde", "Sanitize prompts (recommended)": "Очистити підказки (рекомендовано)", "Automatically adjust generation parameters to ensure free image generations.": "Автоматично налаштовуйте параметри генерації, щоб забезпечити вільне створення зображень.", "Avoid spending Anlas": "Уникайте витра<PERSON>", "Opus tier": "(рівень Opus)", "View my Anlas": "Переглянути мій <PERSON>las", "These settings only apply to DALL-E 3": "Ці налаштування застосовуються лише до DALL-E 3", "Image Style": "Стиль зображення", "Image Quality": "Якість зображення", "Standard": "Станд<PERSON><PERSON>тний", "HD": "HD", "sd_comfy_url": "Приклад: {{comfy_url}}", "Open workflow editor": "Відкрийте редактор робочих процесів", "Create new workflow": "Створіть новий робочий процес", "Delete workflow": "Видалити робочий процес", "Enhance": "Поліпшення", "Refine": "Уточніть", "Decrisper": "Декриспер", "Sampling steps": "Етапи вибірки ()", "Width": "Ши<PERSON>ина ()", "Height": "Висота ()", "Resolution": "резолюція", "Model": "Модель", "Sampling method": "Метод відбору проб", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (підтримуються не всі семплери)", "SMEA versions of samplers are modified to perform better at high resolution.": "Версії семплерів SMEA модифіковано для кращої роботи при високій роздільній здатності.", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "Варіанти DYN семплерів SMEA часто дають більш різноманітний вихід, але можуть вийти з ладу при дуже високій роздільній здатності.", "DYN": "DYN", "Scheduler": "Планувальник", "Restore Faces": "Відновити обличчя", "Hires. Fix": "Наймає. Виправити", "Upscaler": "Upscaler", "Upscale by": "Висококласний за", "Denoising strength": "Сила шумозаглушення", "Hires steps (2nd pass)": "Наймає кроки (2-й прохід)", "Preset for prompt prefix and negative prompt": "Попереднє налаштування для префікса підказки та негативного підказки", "Style": "Стиль", "Save style": "Зберегти стиль", "Delete style": "Видалити стиль", "Common prompt prefix": "Загальний префікс підказки", "sd_prompt_prefix_placeholder": "Використовуйте {prompt}, щоб указати, куди буде вставлено згенерований запит", "Negative common prompt prefix": "Негативний загальний префікс запиту", "Character-specific prompt prefix": "Префікс підказки для певного символу", "Won't be used in groups.": "Не буде використовуватися в групах.", "sd_character_prompt_placeholder": "Будь-які характеристики, які описують поточного обраного персонажа. Буде додано після загального префікса запиту.\nПриклад: жінка, зелені очі, каштанове волосся, рожева сорочка", "Character-specific negative prompt prefix": "Специфічний негативний префікс підказки", "sd_character_negative_prompt_placeholder": "Будь-які характеристики, які не повинні з’являтися для вибраного персонажа. Буде додано після негативного загального префікса запиту.\nПриклад: прикраси, взуття, окуляри", "Shareable": "Можна поділитися", "Image Prompt Templates": "Шаблони підказок із зображеннями", "Vectors Model Warning": "Рекомендується очистити вектори під час зміни моделі в середині чату. Інакше це призведе до негативних результатів.", "Translate files into English before processing": "Перекладіть файли англійською мовою перед обробкою", "Manager Users": "Керувати користувачами", "New User": "Новий користувач", "Status:": "Статус:", "Created:": "Створено:", "Display Name:": "Відображуване ім'я:", "User Handle:": "Ідентифіка<PERSON>ор користувача:", "Password:": "Пароль:", "Confirm Password:": "Підтвердьте пароль:", "This will create a new subfolder...": "Це створить нову вкладену папку в каталозі /data/ з ідентифікатором користувача як ім’я папки.", "Current Password:": "Поточний пароль:", "New Password:": "Новий пароль:", "Confirm New Password:": "Підтвердити новий пароль:", "Debug Warning": "Функції цієї категорії призначені лише для досвідчених користувачів. Не натискайте нічого, якщо ви не впевнені в наслідках.", "Execute": "Виконати", "Are you sure you want to delete this user?": "Ви впевнені, що хочете видалити цього користувача?", "Deleting:": "Видалення:", "Also wipe user data.": "Також стерти дані користувача.", "Warning:": "УВАГА:", "This action is irreversible.": "Ця дія незворотна.", "Type the user's handle below to confirm:": "Введіть дескриптор користувача нижче, щоб підтвердити:", "Import Characters": "Імпорт символів", "Enter the URL of the content to import": "Введіть URL-адресу вмісту для імпорту", "Supported sources:": "Підтримувані джерела:", "char_import_1": "Головний персонаж (пряме посилання або ідентифікатор)", "char_import_example": "приклад:", "char_import_2": "Chub Lorebook (пряме посилання або ID)", "char_import_3": "Символ JanitorAI (пряме посилання або UUID)", "char_import_4": "Символ Pygmalion.chat (пряме посилання або UUID)", "char_import_5": "Символ AICharacterCards.com (пряме посилання або ідентифікатор)", "char_import_6": "Пряме посилання на PNG (див", "char_import_7": "для дозволених хостів)", "char_import_8": "Персо<PERSON><PERSON>isuRealm (пряме посилання)", "char_import_9": "Персо<PERSON><PERSON> (пряме посилання)", "char_import_10": "Перс<PERSON><PERSON><PERSON> (пряме посилання або UUID + .gz)", "Supports importing multiple characters.": "Підтримується імпорт кількох символів.", "Write each URL or ID into a new line.": "Напишіть кожну URL-адресу або ідентифікатор у новому рядку.", "Export for character": "Експорт для персонажа", "Export prompts for this character, including their order.": "Експортуйте підказки для цього персонажа, включаючи їх порядок.", "Export all": "Експортувати все", "Export all your prompts to a file": "Експортуйте всі підказки у файл", "Insert prompt": "Вставити підказку", "Delete prompt": "Видалити підказку", "Import a prompt list": "Імпортувати список підказок", "Export this prompt list": "Експортувати цей список підказок", "Reset current character": "Скинути поточного персонажа", "New prompt": "Нова підказка", "Prompts": "Підказки", "Total Tokens:": "Загальна кількість токенів:", "prompt_manager_tokens": "Жетони", "Are you sure you want to reset your settings to factory defaults?": "Ви впевнені, що бажаєте скинути налаштування до заводських?", "Don't forget to save a snapshot of your settings before proceeding.": "Перш ніж продовжити, не забудьте зберегти знімок ваших налаштувань.", "Settings Snapshots": "Налаштування Знімки", "Record a snapshot of your current settings.": "Запишіть знімок поточних налаштувань.", "Make a Snapshot": "Зробіть знімок", "Restore this snapshot": "Відновити цей знімок", "Hi,": "Прив<PERSON><PERSON>,", "To enable multi-account features, restart the SillyTavern server with": "Щоб увімкнути функції кількох облікових записів, перезапустіть сервер SillyTavern за допомогою", "set to true in the config.yaml file.": "встановити значення true у файлі config.yaml.", "Account Info": "Інформація про обліковий запис", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "Щоб змінити свій аватар користувача, скористайтеся кнопками нижче або виберіть персонаж за замовчуванням у меню «Керування особами».", "Set your custom avatar.": "Встановіть свій власний аватар.", "Remove your custom avatar.": "Видаліть свій власний аватар.", "Handle:": "Ручка:", "This account is password protected.": "Цей обліковий запис захищено паролем.", "This account is not password protected.": "Цей обліковий запис не захищено паролем.", "Account Actions": "Дії облікового запису", "Change Password": "Змінити пароль", "Manage your settings snapshots.": "Керуйте своїми знімками налаштувань.", "Download a complete backup of your user data.": "Завантажте повну резервну копію ваших даних користувача.", "Download Backup": "Завантажте резервну копію", "Danger Zone": "НЕБЕЗПЕЧНА ЗОНА", "Reset your settings to factory defaults.": "Скиньте налаштування до заводських.", "Reset Settings": "Скинути налаштування", "Wipe all user data and reset your account to factory settings.": "Видаліть усі дані користувача та скиньте обліковий запис до заводських налаштувань.", "Reset Everything": "Скинути все", "Reset Code:": "Скинути код:", "Want to update?": "Хочете оновити?", "How to start chatting?": "Як почати спілкування?", "Click _space": "Натисніть", "and select a": "і виберіть", "Chat API": "API чату", "and pick a character.": "і виберіть персонажа.", "You can browse a list of bundled characters in the": "Ви можете переглянути список об’єднаних символів у", "Download Extensions & Assets": "Завантажити розширення та ресурси", "menu within": "меню всередині", "Confused or lost?": "Збентежені чи загублені?", "click these icons!": "клацніть на ці іконки!", "in the chat bar": "в рядку чату", "SillyTavern Documentation Site": "Сайт документації SillyTavern", "Extras Installation Guide": "Посібник з встановлення додаткових компонентів", "Still have questions?": "Все ще є питання?", "Join the SillyTavern Discord": "Приєднуйтесь до <PERSON><PERSON><PERSON><PERSON><PERSON>rd", "Post a GitHub issue": "Опублікуйте проблему на GitHub", "Contact the developers": "Зв'яжіться з розробниками"}