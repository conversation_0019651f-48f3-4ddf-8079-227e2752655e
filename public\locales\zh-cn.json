{"Favorite": "星标", "Tag": "标签", "Duplicate": "复制", "Persona": "用户角色", "Delete": "删除", "AI Response Configuration": "AI响应配置", "AI Configuration panel will stay open": "AI配置面板将保持打开", "clickslidertips": "单击滑块以手动输入值。", "MAD LAB MODE ON": "疯狂实验室模式开启", "Documentation on sampling parameters": "有关采样参数的文档", "kobldpresets": "Kobold 预设", "Import preset": "导入预设", "Export preset": "导出预设", "Delete the preset": "删除预设", "guikoboldaisettings": "KoboldAI 用户界面设置", "Update current preset": "更新当前预设", "Rename current preset": "重命名当前预设", "Save preset as": "另存预设为", "Restore current preset": "恢复当前预设", "novelaipresets": "NovelAI 预设", "Default": "默认", "openaipresets": "对话补全预设", "Text Completion presets": "文本补全预设", "response legth(tokens)": "回复长度（以词符数计）", "Streaming": "流式传输", "Streaming_desc": "逐位显示生成的回复", "context size(tokens)": "上下文长度（以词符数计）", "unlocked": "解锁", "Only enable this if your model supports context sizes greater than 8192 tokens": "仅在您的模型支持大于8192个词符的上下文长度时启用此选项", "Max prompt cost:": "最大提示词费用：", "AI Module": "AI 模块", "Changes the style of the generated text.": "更改生成文本的样式。", "No Module": "无模块", "Instruct": "指导", "Prose Augmenter": "散文增强器", "Text Adventure": "文字冒险", "Temperature": "温度", "rep.pen": "重复惩罚", "Rep. Pen. Range.": "重复惩罚范围。", "Rep. Pen. Slope": "重复惩罚斜率", "Rep. Pen. Freq.": "频率重复惩罚", "Rep. Pen. Presence": "存在重复惩罚", "Min P": "<PERSON>", "TFS": "无尾采样", "Top P": "Top P", "Top A": "Top A", "Top K": "Top K", "Mirostat Tau": "Mirostat τ", "Mirostat LR": "Mirostat 学习率", "Typical P": "典型P", "Linear": "Linear", "Quad": "Quad", "Conf": "Conf", "Min Length": "最小长度", "Phrase Repetition Penalty": "短语重复惩罚", "Off": "关闭", "Very light": "非常轻", "Light": "轻", "Medium": "中", "Aggressive": "激进", "Very aggressive": "非常激进", "Preamble": "序文", "Restore default prompt": "恢复默认提示词", "Use style tags to modify the writing style of the output.": "使用样式标签修改输出的写作风格。", "Banned Tokens": "禁用的词符", "Sequences you don't want to appear in the output. One per line.": "您不希望出现在输出中的字符串。 每行一个。", "Logit Bias": "Logit 偏置", "Add": "添加", "Helps to ban or reenforce the usage of certain words": "有助于禁止或加强某些单词的使用", "Unlocked Context Size": "解锁上下文长度", "Unrestricted maximum value for the context slider": "AI可见的最大上下文长度", "Context Size (tokens)": "上下文长度（以词符数计）", "Max Response Length (tokens)": "最大回复长度（以词符数计）", "Multiple swipes per generation": "每次生成多个备选回复", "Middle-out Transform": "Middle-out Transform", "Auto": "Auto", "Allow": "Allow", "Forbid": "Forbid", "Enable OpenAI completion streaming": "启用OpenAI文本补全流式传输", "Display the response bit by bit as it is generated.": "随着回复的生成，逐词逐句地显示结果。", "When this is off, responses will be displayed all at once when they are complete.": "当此选项关闭时，回复将在完成后一次性显示。", "Frequency Penalty": "频率惩罚", "Presence Penalty": "存在惩罚", "Repetition Penalty": "重复惩罚", "Quick Prompts Edit": "快速提示词编辑", "Main": "主要", "Auxiliary": "辅助的", "Post-History Instructions": "后续历史指令", "Utility Prompts": "实用提示词", "Impersonation prompt": "AI帮答提示词", "Prompt that is used for Impersonation function": "用于AI帮答功能的提示词", "World Info Format Template": "世界信息格式模板", "Restore default format": "恢复默认格式", "Wraps activated World Info entries before inserting into the prompt.": "在插入提示词之前包裹激活的世界信息条目。", "scenario_format_template_part_1": "使用", "scenario_format_template_part_2": "标记插入内容的位置。", "Scenario Format Template": "场景格式模板", "Personality Format Template": "角色设定格式模板", "Group Nudge Prompt Template": "群聊推进提示词模板", "Sent at the end of the group chat history to force reply from a specific character.": "在群聊记录的末尾发送，以强制特定角色回复。", "New Chat": "新聊天", "Restore new chat prompt": "恢复新的聊天提示词", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "设置在聊天历史的开头，表示新的聊天即将开始。", "New Group Chat": "新群聊", "Restore new group chat prompt": "恢复默认提示词", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "设置在聊天记录的开头，表示新的群聊即将开始。", "New Example Chat": "新示例聊天", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "设置在对话示例的开头，以表明新的示例聊天即将开始。", "Continue nudge": "继续推进", "Set at the end of the chat history when the continue button is pressed.": "当按下继续按钮时在聊天记录的末尾设置。", "Replace empty message": "替换空消息", "Send this text instead of nothing when the text box is empty.": "当文本框为空时，发送此文本而不是空白。", "Seed": "种子", "Set to get deterministic results. Use -1 for random seed.": "设置此值以得到确定性结果。使用 -1 表示随机种子。", "Temperature controls the randomness in token selection": "温度控制词符选择中的随机性：\n- 低温（<1.0）导致更可预测的文本，优先选择高概率的词符。\n- 高温（>1.0）鼓励创造性和输出的多样性，更多地选择低概率的词符。\n将值设置为 1.0 以使用原始概率。", "Top_K_desc": "Top K 设定了可以选择的最高概率词符的最大数量。", "Top_P_desc": "Top P（又称核采样）将所有高概率词符聚集在一起，直到达到特定的百分比。\n换句话说，如果前两个词符分别都有 25% 的概率，而 Top-P 为 0.50，那么只有这两个词符会被考虑。\n将这个值设置为 1.0 就相当于关闭了这个功能。", "Typical_P_desc": "典型P采样会根据词符与整体熵的平均差异来优先选择词符。\n那些累积概率接近特定阈值（比如 0.5）的词符会被保留，这样就能区分出那些含有平均信息量的词符。\n将这个值设置为 1.0 就相当于关闭了这个功能。", "Min_P_desc": "Min P 设定了一个基础的最小概率，它会根据最高词符概率来进行优化。\n如果最高词符概率是 80%，而Min P设定为 0.1，那么只有那些概率高于8%的词符会被考虑。\n将这个值设置为 0 就相当于关闭了这个功能。", "Top_A_desc": "Top A 设定了一个阈值，用于根据最高词符概率的平方来选择词符。\n如果 Top A 设定为 0.2，而最高词符概率是 50%，那么概率低于 5% 的词符会被排除（0.2 * 0.5^2）。\n将这个值设置为 0 就相当于关闭了这个功能。", "Tail_Free_Sampling_desc": "无尾采样（TFS）通过分析词符概率变化率以及二阶导数来搜索分布中概率较低的尾部词符，\n词符会被保留到某个阈值（例如 0.3），这取决于统一的二阶导数。\n这个值越接近 0，被拒绝的词符数量就越多。将这个值设置为 1.0 就相当于关闭了这个功能。", "rep.pen range": "重复惩罚范围", "Mirostat": "Mirostat", "Mode": "模式", "Mirostat_Mode_desc": "值为 0 表示完全禁用 Mirostat。1 表示 Mirostat 1.0，2 表示 Mirostat 2.0", "Tau": "τ", "Mirostat_Tau_desc": "控制 Mirostat 输出的变化", "Eta": "η", "Mirostat_Eta_desc": "控制 Mirostat 的学习率", "Ban EOS Token": "禁止 EOS 词符", "Ban_EOS_Token_desc": "禁止使用 KoboldCpp 的序列结束 (EOS) 词符（可能还禁止使用 KoboldAI 的其他词符）。适合故事写作，但不应用于聊天和指导模式。", "GBNF Grammar": "GBNF 语法", "Type in the desired custom grammar": "输入所需的自定义语法", "Samplers Order": "采样器顺序", "Samplers will be applied in a top-down order. Use with caution.": "采样器将按自上而下的顺序应用。请谨慎使用。", "Tail Free Sampling": "无尾采样", "Load koboldcpp order": "加载koboldcpp顺序", "Top K Sampling": "Top K 采样", "Nucleus Sampling": "核采样", "Top A Sampling": "Top A 采样", "Unified Sampling": "Unified Sampling", "Neutralize Samplers": "置采样器参数为失效值", "Set all samplers to their neutral/disabled state.": "将所有采样器设置为失效/禁用状态。", "Sampler Select": "采样器选择", "Customize displayed samplers or add custom samplers.": "自定义显示的采样器或添加自定义采样器。", "Epsilon Cutoff": "ε 截断", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "ε 截断设置了一个概率下限，低于该下限的词符将被排除在采样之外。\n以 1e-4 单位；合适的值为 3。将其设置为 0 以禁用。", "Top nsigma": "Top nsigma", "Eta Cutoff": "η 截断", "Eta_Cutoff_desc": "η截断是特殊η采样技术的主要参数。&#13;以1e-4为单位；合理的值为3。&#13;设置为0以禁用。&#13;有关详细信息，请参阅Hewitt等人的论文《Truncation Sampling as Language Model Desmoothing》（2022年）。", "rep.pen decay": "重复惩罚衰减", "Encoder Rep. Pen.": "编码器重复惩罚", "No Repeat Ngram Size": "无重复n-gram大小", "Skew": "倾斜", "Max Tokens Second": "每秒最大词符数", "Smooth Sampling": "平滑采样", "Smooth_Sampling_desc": "允许您使用二次/三次变换来调整分布。较低的平滑因子值将更具创造性，通常在 0.2-0.3 之间是最佳点（假设曲线 = 1）。较高的平滑曲线值将使曲线更陡峭，这将更积极地惩罚低概率选择。1.0 曲线相当于仅使用平滑因子。", "Smoothing Factor": "平滑系数", "Smoothing Curve": "平滑曲线", "Exclude Top Choices (XTC)": "Exclude Top Choices (XTC)", "Threshold": "阈值", "Probability": "可能性", "DRY_Repetition_Penalty_desc": "DRY 会对那些会将输入末尾扩展为输入中之前出现过的序列的词符进行惩罚。将乘数设置为 0 以禁用。", "DRY Repetition Penalty": "DRY 重复惩罚", "DRY_Multiplier_desc": "将值设置为 > 0 以启用 DRY。控制最短惩罚序列的惩罚幅度。", "Multiplier": "乘数", "DRY_Base_desc": "控制惩罚随序列长度的增加而增长的速度。", "Base": "基数", "DRY_Allowed_Length_desc": "可以重复而不会受到惩罚的最长序列长度。", "Allowed Length": "允许长度", "Penalty Range": "惩罚范围", "DRY_Sequence_Breakers_desc": "序列匹配不再继续的词符。用逗号分隔的带引号的字符串列表指定。", "Sequence Breakers": "序列打断符", "JSON-serialized array of strings.": "JSON 序列化的字符串数组。", "Dynamic Temperature": "动态温度", "Scale Temperature dynamically per token, based on the variation of probabilities": "根据概率的变化动态地缩放每个词符的温度。", "Minimum Temp": "最小温度", "Maximum Temp": "最大温度", "Exponent": "指数", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat（mode=1 仅用于 llama.cpp）", "Mirostat_desc": "Mirostat 是一个用于控制输出困惑度的恒温器", "Mirostat Mode": "Mirostat 模式", "Variability parameter for Mirostat outputs": "Mirostat 输出的变异性参数。", "Mirostat Eta": "Mirostat η", "Learning rate of Mirostat": "Mirostat 的学习率。", "Beam search": "束搜索", "A greedy, brute-force algorithm used in LLM sampling to find the most likely sequence of words or tokens. It expands multiple candidate sequences at once, maintaining a fixed number (beam width) of top sequences at each step.": "一种在LLM采样中使用的贪婪暴力算法，用于找到最可能的单词或标记序列。它一次扩展多个候选序列，在每一步保留固定数量（光束宽度）的最佳序列。", "# of Beams": "光束数量", "The number of sequences generated at each step with Beam Search.": "The number of sequences generated at each step with Beam Search.", "Length Penalty": "长度惩罚", "Penalize sequences based on their length.": "Penalize sequences based on their length.", "Early Stopping": "提前停止", "Controls the stopping condition for beam search. If checked, the generation stops as soon as there are '# of Beams' sequences. If not checked, a heuristic is applied and the generation is stopped when it's very unlikely to find better candidates.": "控制光束搜索的停止条件。勾选时，当生成到达‘光束数量’的序列时停止。如果未勾选，则采用启发式方法，当几乎不可能找到更好的候选项时停止生成。", "Contrastive search": "对比搜索", "Contrastive_search_txt": "通过利用大多数 LLM 表示空间的各向同性以鼓励多样性同时保持一致性的采样器。详见 Su 等人在 2022 年发表的论文 《A Contrastive Framework for Neural Text Generation》。", "Penalty Alpha": "惩罚系数 α", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "对比搜索正则化项的强度。 将值设置为 0 以禁用对比搜索。", "Do Sample": "进行采样", "Add BOS Token": "添加序列开始词符", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "在提示词的开头添加序列开始词符。 禁用此功能可以使回复更具创意", "Ban the eos_token. This forces the model to never end the generation prematurely": "禁止序列结束词符。 这将强制模型永远不会提前结束生成", "Ignore EOS Token": "忽略序列结束词符", "Ignore the EOS Token even if it generates.": "即使生成了序列结束词符，也忽略它。", "Skip Special Tokens": "跳过特殊词符", "Request Model Reasoning": "Request Model Reasoning", "Temperature Last": "温度放最后", "Temperature_Last_desc": "温度采样器放到最后使用。这通常是合理的。\n当启用时：首先进行潜在词符的选择，然后应用温度来修正它们的相对概率（技术上是对数似然）。\n当禁用时：首先应用温度来修正所有词符的相对概率，然后从中选择潜在词符。\n禁用此项可以增大分布在尾部的词符概率，这可能加大得到不相关回复的几率。", "Speculative Ngram": "推测性 Ngram", "Use a different speculative decoding method without a draft model": "使用不同的推测解码方法（不采用草稿模型）。最好使用草稿模型。推测性 Ngram 的效果不太好。", "Spaces Between Special Tokens": "特殊词符之间的空格", "Seed_desc": "一个用于生成确定性和可复现的输出的随机种子。设置为 -1 时会使用随机种子。", "LLaMA / Mistral / Yi models only": "LLaMA / Mistral / Yi模型专用。首先确保您选择了适当的词符化器。\n这项设置决定了你不想在结果中看到的字符串。\n每行一个字符串。可以是文本或者[词符id]。\n许多词符以空格开头。如果不确定，请使用词符计数器。", "Global list": "Global list", "Example: some text [42, 69, 1337]": "例如：\n一些文本\n[42, 69, 1337]", "Preset-specific list": "Preset-specific list", "CFG": "CFG", "Classifier Free Guidance. More helpful tip coming soon": "无分类器指导（CFG）。更多有用的提示敬请期待。", "Scale": "缩放比例", "Negative Prompt": "负面提示词", "Used if CFG Scale is unset globally, per chat or character": "如果CFG缩放比例未被全局设置，它将作用于所有聊天或角色", "Add text here that would make the AI generate things you don't want in your outputs.": "请在此处添加文本，以避免生成您不希望出现在输出中的内容。", "Grammar String": "语法字符串", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF 或 EBNF，取决于使用的后端。如果您使用这个，您应该知道该用哪一个。", "JSON Schema": "JSON 结构", "Type in the desired JSON schema": "输入所需的 JSON 结构", "Top P & Min P": "Top P 和 Min P", "Load default order": "加载默认顺序", "Sampler Order": "取样器顺序", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "仅限 llama.cpp。确定采样器的顺序。如果 Mirostat 模式不为 0，则忽略采样器顺序。", "Sampler Priority": "采样器优先级", "Ooba only. Determines the order of samplers.": "确定采样器的顺序（仅适用于Ooba）", "Aphrodite only. Determines the order of samplers. Skew is always applied post-softmax, so it's not included here.": "仅适用于 Aphrodite，用于确定采样器的顺序。偏移（Skew）始终在 softmax 后应用，因此不包含在这里。", "Aphrodite only. Determines the order of samplers.": "仅适用于 A<PERSON>rodite。用于确定采样器的顺序。", "Character Names Behavior": "角色名称行为", "Helps the model to associate messages with characters.": "有助于模型将消息与角色关联起来。", "None": "无", "character_names_none": "不添加角色名称前缀。在群聊中可能导致错误行为，谨慎勾选。", "character_names_default": "群聊和过去的角色除外。否则，请确保在提示词中提供了姓名。", "Completion Object": "补全对象", "character_names_completion": "适用限制：仅限拉丁字母数字和下划线。不适用于所有补全源，尤其是：Claude、MistralAI、Google。", "Message Content": "消息内容", "Prepend character names to message contents.": "在消息内容中添加角色名称。", "Continue Postfix": "继续后缀", "The next chunk of the continued message will be appended using this as a separator.": "将以此为分隔符附加后续消息的下一块。", "Space": "空格", "Newline": "换行", "Double Newline": "双换行", "Wrap user messages in quotes before sending": "在发送之前将用户消息用引号括起来", "Wrap in Quotes": "用引号包裹", "Wrap entire user message in quotes before sending.": "在发送之前用引号包裹整个用户消息。", "Leave off if you use quotes manually for speech.": "如果您手动使用引号包裹对话，请忽略此项。", "Continue prefill": "继续预填充", "Continue sends the last message as assistant role instead of system message with instruction.": "继续发送的是作为助手角色的最后一条消息，而不是带有指示的系统消息。", "Squash system messages": "压缩系统消息", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "将连续的系统消息合并为一条（不包括示例对话），可能会提高一些模型的连贯性。", "Enable function calling": "启用函数调用", "enable_functions_desc_1": "允许使用", "enable_functions_desc_2": "功能工具", "enable_functions_desc_3": "可以被各种扩展利用来提供附加功能。", "Send inline images": "发送图片", "image_inlining_hint_1": "如果模型支持，就可以在提示词中发送图片。\n发送消息时，点击", "image_inlining_hint_2": "在这里（", "image_inlining_hint_3": "）将图片添加到消息中。", "Inline Image Quality": "图片画质", "openai_inline_image_quality_auto": "自动", "openai_inline_image_quality_low": "低", "openai_inline_image_quality_high": "高", "Use system prompt": "使用系统提示词", "Merges_all_system_messages_desc_1": "合并所有系统消息，直到第一条具有非系统角色的消息，然后通过", "Merges_all_system_messages_desc_2": "字段发送。", "Request model reasoning": "请求思维链", "Allows the model to return its thinking process.": "允许模型返回其思维过程。", "Constrains effort on reasoning for reasoning models.": "限定模型推理的强度。\n当前支持低、中、高三种强度。\n降低推理强度可以让模型更快回复，并节省推理所用的词符数。。", "Reasoning Effort": "推理强度", "openai_reasoning_effort_low": "低", "openai_reasoning_effort_medium": "中", "openai_reasoning_effort_high": "高", "Assistant Prefill": "AI预填", "Expand the editor": "展开编辑器", "Start Claude's answer with...": "以如下内容开始Claude的回答...", "Assistant Impersonation Prefill": "AI帮答预填", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "为支持的模型发送系统提示词。如果禁用，则用户消息将添加到提示词的开头。", "Confirm token parsing with": "确认使用以下工具进行词符解析", "Tokenizer": "词符化器", "New preset": "新预设", "Delete preset": "删除预设", "View / Edit bias preset": "查看/编辑偏置预设", "Add bias entry": "添加偏置条目", "openai_logit_bias_no_items": "没有相关产品", "Most tokens have a leading space.": "大多数词符都有一个前导空格。", "API Connections": "API连接", "api_no_connection": "无连接...", "Text Completion": "文本补全", "Chat Completion": "聊天补全", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "智谱AI API Key": "智谱AI API密钥", "智谱AI Model": "智谱AI模型", "通义千问 API Key": "通义千问 API密钥", "通义千问 Model": "通义千问模型", "文心一言 API Key": "文心一言 API密钥", "文心一言 Model": "文心一言模型", "RWKV Model": "RWKV 模型", "智谱AI (GLM)": "智谱AI (GLM)", "通义千问 (Qwen)": "通义千问 (<PERSON><PERSON>)", "文心一言 (ERNIE)": "文心一言 (ERNIE)", "RWKV": "RWKV", "AI Horde Website": "AI Horde 网站", "Avoid sending sensitive information to the Horde.": "避免向 Horde 发送敏感信息。", "Review the Privacy statement": "查看隐私声明", "Register a Horde account for faster queue times": "注册 Horde 帐户以加快排队时间", "Learn how to contribute your idle GPU cycles to the Horde": "了解如何将您的空闲GPU时钟周期共享给 Horde", "Adjust context size to worker capabilities": "根据工作单元能力调整上下文长度", "Adjust response length to worker capabilities": "根据工作单元能力调整响应长度", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "可以通过仅排队认证的工作单元来帮助处理不良回复。这可能会减慢回复速度。", "Trusted workers only": "仅信任的工作单元", "API key": "API密钥", "Get it here:": "在此获取：", "Register": "注册", "View my Kudos": "查看我的荣誉", "Enter": "输入", "to use anonymous mode.": "以使用匿名模式。", "Clear your API key": "清除您的API密钥", "For privacy reasons, your API key will be hidden after you reload the page.": "出于隐私原因，重新加载页面后您的 API 密钥将被隐藏。", "Models": "模型", "Refresh models": "刷新模型", "-- Horde models not loaded --": "-- Horde 模型未加载 --", "Not connected...": "未连接...", "API url": "API地址", "Example: http://127.0.0.1:5000/api ": "示例：http://127.0.0.1:5000/api", "Connect": "连接", "Cancel": "取消", "Novel API key": "Novel AI API 密钥", "Get your NovelAI API Key": "获取您的 NovelAI API 密钥", "Enter it in the box below": "在下面的框中输入", "Novel AI Model": "Novel AI 模型", "No connection...": "无连接...", "API Type": "API 类型", "Generic (OpenAI-compatible) [LM Studio, LiteLLM, etc.]": "通用（兼容 OpenAI）[LM Studio、LiteLLM 等]", "LM Studio Endpoint (Base URL)": "LM Studio 端点（基础URL）", "LM Studio Model": "LM Studio 模型", "Test Connection": "测试连接", "Not connected": "未连接", "Default LM Studio server runs on": "默认 LM Studio 服务器运行在", "completions note prefix": "", "suffix will be added automatically.": "后缀将自动添加。", "Example: http://localhost:1234/v1": "例如：http://localhost:1234/v1", "TogetherAI API Key": "TogetherAI API 密钥", "TogetherAI Model": "TogetherAI 模型", "-- Connect to the API --": "-- 连接到API --", "OpenRouter API Key": "OpenRouter API 密钥", "Click Authorize below or get the key from": "点击下方授权或从以下位置获取密钥", "View Remaining Credits": "查看剩余额度", "OpenRouter Model": "OpenRouter 模型", "Model Providers": "模型提供商", "Automatically chooses an alternative provider if chosen providers can't serve your request.": "在当前选择的模型提供商无效时，自动选择备用的提供商。", "Allow fallback providers": "允许后备提供者", "InfermaticAI API Key": "InfermaticAI API 密钥", "InfermaticAI Model": "InfermaticAI 模型", "DreamGen API key": "DreamGen API 密钥", "DreamGen Model": "DreamGen 模型", "Mancer API key": "Mancer API 密钥", "Mancer Model": "<PERSON><PERSON> 模型", "API key (optional)": "API密钥（可选）", "Server url": "服务器URL", "Example: http://127.0.0.1:5000": "示例：http://127.0.0.1:5000", "Model ID (optional)": "模型 ID（可选）", "Make sure you run it with": "确保您在运行时加上", "flag": "标志", "Custom model (optional)": "自定义模型（可选）", "Featherless Model Selection": "Featherless Model Selection", "Search...": "搜索...", "Search": "搜索", "category": "分类", "Top": "Top", "New": "新建", "All": "All", "class": "All Classes", "Toggle grid view": "切换网格视图", "No model description": "[无描述]", "vllm-project/vllm": "vllm-project/vllm（OpenAI API 包装器模式）", "vLLM API key": "vLLM API 密钥", "Example: http://127.0.0.1:8000": "示例：http://127.0.0.1:8000", "vLLM Model": "vLLM 模型", "HuggingFace Token": "HuggingFace 代币", "Endpoint URL": "端点 URL", "Example: https://****.endpoints.huggingface.cloud": "例如：https://****.endpoints.huggingface.cloud", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine（用于OpenAI API的包装器）", "Aphrodite API key": "Aphrodite API 密钥", "Aphrodite Model": "Aphrodite 模型", "ggerganov/llama.cpp": "ggerganov/llama.cpp", "Example: http://127.0.0.1:8080": "示例：http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "示例：http://127.0.0.1:11434", "Ollama Model": "Ollama 模型", "Download": "下载", "Tabby API key": "Tabby API 密钥", "Tabby Model": "<PERSON><PERSON> 模型", "must be set in Tabby's config.yml to switch models.": "必须在Tabby的config.yml内设置以切换模型", "Use an admin API key.": "使用管理员API密钥。", "koboldcpp API key (optional)": "koboldcpp API 密钥（可选）", "Example: http://127.0.0.1:5001": "示例：http://127.0.0.1:5001", "Bypass status check": "跳过状态检查", "Derive context size from backend": "从后端获取上下文长度", "Authorize": "授权", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "使用OAuth流程获取您的OpenRouter API令牌。您将被重定向到openrouter.ai", "Chat Completion Source": "聊天补全来源", "Custom (OpenAI-compatible)": "自定义（兼容 OpenAI）", "Reverse Proxy": "反向代理", "Proxy Presets": "代理预设", "Saved addresses and passwords.": "保存的地址和密码。", "Save Proxy": "保存代理", "Delete Proxy": "删除代理", "Proxy Name": "代理名称", "This will show up as your saved preset.": "这将显示为您保存的预设。", "Proxy Server URL": "代理服务器 URL", "Alternative server URL (leave empty to use the default value).": "备用服务器 URL（留空以使用默认值）。", "Doesn't work? Try adding": "不起作用？在末尾添加", "at the end!": "试试！", "Proxy Password": "代理密码", "Will be used as a password for the proxy instead of API key.": "将用作代理的密码，而不是 API 密钥。", "Peek a password": "查看密码", "Using a proxy that you're not running yourself is a risk to your data privacy.": "使用您自己未运行的代理会对您的数据隐私造成风险。", "ANY support requests will be REFUSED if you are using a proxy.": "如果您使用代理，任何支持请求都将被拒绝。", "Do not proceed if you do not agree to this!": "如果您不同意，请不要继续！", "OpenAI API key": "OpenAI API 密钥", "View API Usage Metrics": "查看API使用情况", "Follow": "按照", "these directions": "这些步骤", "to get your OpenAI API key.": "获取您的 OpenAI API 密钥。", "Use Proxy password field instead. This input will be ignored.": "请使用“代理密码”字段。此输入将被忽略。", "OpenAI Model": "OpenAI 模型", "Bypass API status check": "绕过API状态检查", "Show External models (provided by API)": "显示外部模型（由API提供）", "Claude API Key": "Claude API 密钥", "Get your key from": "从以下位置获取您的密钥", "Anthropic's developer console": "Anthropic 开发者控制台", "Claude Model": "<PERSON> 模型", "Window AI Model": "Window AI 模型", "Use extension settings": "使用扩展程序中的设定", "Allow fallback routes Description": "如果所选模型无法响应您的请求，则自动选择备用模型。", "Allow fallback models": "允许后备模型", "Model Order": "OpenRouter 模型顺序", "Alphabetically": "按字母顺序", "Price": "价格（最便宜）", "Context Size": "上下文长度", "Group by vendors": "按厂商分组", "Group by vendors Description": "将 OpenAI 模型放在一组，将 Anthropic 模型放在另一组，等等。可以与排序结合。", "To use instruct formatting, switch to OpenRouter under Text Completion API.": "To use instruct formatting, switch to OpenRouter under Text Completion API.", "AI21 API Key": "AI21 API 密钥", "AI21 Model": "AI21 模型", "Google AI Studio API Key": "Google AI Studio API 密钥", "Google Model": "Google 模型", "MistralAI API Key": "MistralAI API 密钥", "MistralAI Model": "MistralAI 模型", "Groq API Key": "Groq API 密钥", "Groq Model": "Groq 模型", "NanoGPT API Key": "NanoGPT API Key", "NanoGPT Model": "NanoGPT Model", "DeepSeek API Key": "DeepSeek API 密钥", "DeepSeek Model": "DeepSeek 模型", "Perplexity API Key": "Perplexity API 密钥", "Perplexity Model": "Perplexity 模型", "Cohere API Key": "Cohere API 密钥", "Cohere Model": "Cohere 模型", "Block Entropy API Key": "Block Entropy API 密钥", "Select a Model": "选择一个模型", "Custom Endpoint (Base URL)": "自定义端点（基础 URL）", "Custom API Key": "自定义 API 密钥", "(Optional)": "（可选）", "Enter a Model ID": "输入模型名", "Example: gpt-4o": "例如：gpt-4o", "Available Models": "可用模型", "Prompt Post-Processing": "提示词后处理", "Applies additional processing to the prompt before sending it to the API.": "在将提示词发送到 API 之前对其进行额外处理。", "prompt_post_processing_none": "未选择", "prompt_post_processing_merge": "合并相同角色连续的发言", "prompt_post_processing_semi": "半严格（强制对话角色交替）", "prompt_post_processing_strict": "严格（强制对话角色交替、用户最先）", "01.AI API Key": "01.AI API密钥", "01.AI Model": "01.AI 模型", "Additional Parameters": "附加参数", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "通过发送简短的测试消息验证您的API连接。请注意，您将因此消耗额度！", "Test Message": "发送测试消息", "Auto-connect to Last Server": "自动连接到上次的服务器", "Missing key": "❌ 缺少密钥", "Key saved": "密钥已保存", "View hidden API keys": "查看隐藏的API密钥", "AI Response Formatting": "AI回复格式化", "Advanced Formatting": "高级格式化设置", "Import Advanced Formatting settings": "导入高级格式化设置\n\n对于指导和上下文模板，你也可以提供旧版文件。", "Master Import": "全局导入", "Export Advanced Formatting settings": "导出高级格式化设置", "Master Export": "全局导出", "Context Template": "上下文模板", "context_derived": "若可能，从模型的元数据获取。", "Select your current Context Template": "选择你当前的上下文模板", "Update current template": "更新当前模板", "Rename current template": "重命名当前模板", "Save template as": "将模板另存为", "Import template": "导入模板", "Export template": "导出模板", "Restore current template": "还原当前模板", "Delete the template": "删除模板", "Story String": "故事字符串", "Example Separator": "示例分隔符", "Chat Start": "聊天开始", "Context Formatting": "上下文格式", "Always add character's name to prompt": "始终将角色名称添加到提示词", "Generate only one line per request": "每次请求只生成一行", "Collapse Consecutive Newlines": "折叠连续的换行符", "Trim spaces": "修剪空格", "Disabling is not recommended.": "不建议关闭。", "Trim Incomplete Sentences": "修剪不完整的句子", "Add Chat Start and Example Separator to a list of stopping strings.": "将聊天开始和示例分隔符添加到停止字符串列表中。", "Separators as Stop Strings": "分隔符作为终止字符串", "Add Character and User names to a list of stopping strings.": "将角色和用户名添加到停止字符串列表中。", "Names as Stop Strings": "名称作为终止字符串", "context_allow_post_history_instructions": "如果在角色卡中定义并且启用了“首选角色卡说明”，则在提示末尾包含后历史说明。\n不建议在文本补全模型中使用此功能，否则会导致输出错误。", "Instruct Template": "指导模板", "instruct_derived": "如果可能，从模型元数据中获取", "instruct_bind_to_context": "如果启用，上下文模板将根据所选的指导模板名称或偏好自动选择。", "instruct_enabled": "启用指导模板", "Select your current Instruct Template": "选择你当前的指导模板", "Delete template": "删除模板", "Activation Regex": "激活正则表达式", "instruct_template_activation_regex_desc": "当连接到API或选择模型时，若模型名称与给定的正则表达式匹配，自动启用此指导模板。", "Wrap Sequences with Newline": "用换行符包裹序列", "Replace Macro in Sequences": "替换序列中的宏", "Skip Example Dialogues Formatting": "跳过示例对话格式化", "Include Names": "包括名称", "Never": "永不", "Groups and Past Personas": "群聊和过去的用户角色", "Always": "永远", "Instruct Sequences": "指令序列", "User Message Sequences": "用户消息序列", "Inserted before a User message and as a last prompt line when impersonating.": "插入到用户消息之前并作为模拟时的最后一行提示词。", "User Prefix": "用户消息前缀", "Inserted after a User message.": "插入到用户消息之后。", "User Suffix": "用户消息后缀", "Assistant Message Sequences": "助手消息序列", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "插入到助手消息之前并作为生成 AI 回复时的最后一行提示词。", "Assistant Prefix": "助手消息前缀", "Inserted after an Assistant message.": "插入于助手消息之后。", "Assistant Suffix": "助手消息后缀", "System Message Sequences": "系统消息序列", "Inserted before a System (added by slash commands or extensions) message.": "插入到系统（由快捷命令或扩展添加）消息之前。", "System Prefix": "系统消息前缀", "Inserted after a System message.": "插入到系统消息之后。", "System Suffix": "系统消息后缀", "If enabled, System Sequences will be the same as User Sequences.": "如果启用，系统序列将与用户序列相同。", "System same as User": "系统与用户相同", "System Prompt Sequences": "系统提示词序列", "Inserted before a System prompt.": "插入到系统提示词之前。", "System Prompt Prefix": "系统提示词前缀", "Inserted after a System prompt.": "在系统提示词后插入。", "System Prompt Suffix": "系统提示词后缀", "Misc. Sequences": "杂项序列", "Inserted before the first Assistant's message.": "插入到第一个助理的消息之前。", "First Assistant Prefix": "第一个助理前缀", "instruct_last_output_sequence": "插入到最后一条助手消息之前或作为生成 AI 回复时的最后一行提示词（中立/系统角色除外）。", "Last Assistant Prefix": "最后一个助理前缀", "Inserted before the first User's message.": "插入在第一个用户的消息之前。", "First User Prefix": "第一个用户前缀", "instruct_last_input_sequence": "插入到最后一条用户消息之前。", "Last User Prefix": "上次用户前缀", "Will be inserted as a last prompt line when using system/neutral generation.": "当使用系统/中性生成时将作为最后的一行提示词插入。", "System Instruction Prefix": "系统指令前缀", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "如果生成了停止序列，则该序列之后的所有内容都将从输出中删除（包括在内）。", "Stop Sequence": "停止序列", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "如果聊天记录不是以用户消息开头，则会插入到聊天记录的开头。", "User Filler Message": "用户填写信息", "System Prompt": "系统提示词", "sysprompt_enabled": "启用系统提示词", "Select your current System Prompt": "选择当前的系统提示词", "Update current prompt": "更新当前提示词", "Rename current prompt": "重命名当前提示词", "Save prompt as": "将提示词另存为", "Restore current prompt": "还原当前提示词", "Delete prompt": "删除提示词", "Prompt Content": "提示词内容", "Custom Stopping Strings": "自定义停止字符串", "JSON serialized array of strings": "JSON序列化的字符串数组", "Replace Macro in Stop Strings": "替换自定义停止字符串中的宏", "Token Padding": "词符填充", "Reasoning": "推理", "reasoning_auto_parse": "Automatically parse reasoning blocks from main content between the reasoning prefix/suffix. Both fields must be defined and non-empty.", "Auto-Parse": "自动解析", "reasoning_auto_expand": "自动展开推理内容块。", "Auto-Expand": "自动展开", "reasoning_show_hidden": "对于隐藏推理内容的模型，展示其推理用时。", "Show Hidden": "显示隐藏内容", "reasoning_add_to_prompts": "将已有的推理块添加到提示词。若需新增一个推理块，请使用消息编辑菜单。", "Add to Prompts": "添加到提示词", "reasoning_max_additions": "Maximum number of reasoning blocks to be added per prompt, counting from the last message.", "Max": "最大值", "Reasoning Formatting": "推理内容格式化", "reasoning_prefix": "插入在推理内容之前。", "Prefix": "前缀", "reasoning_suffix": "插入在推理内容之后。", "Suffix": "后缀", "reasoning_separator": "插入在推理内容和消息内容之间。", "Separator": "分隔符", "Miscellaneous": "杂项", "Non-markdown strings": "非 Markdown 字符串", "comma delimited,no spaces between": "以逗号分隔，无需空格", "Start Reply With": "以...开始回复", "Show reply prefix in chat": "在聊天中显示回复前缀", "World Info": "世界信息", "Locked = World Editor will stay open": "锁定 = 世界编辑器将保持打开状态", "Worlds/Lorebooks": "世界/知识书", "Active World(s) for all chats": "已启用的世界（全局有效）", "-- World Info not found --": "-- 未找到世界信息 --", "Global World Info/Lorebook activation settings": "全局世界信息/知识书激活设置", "Click to expand": "单击展开", "Scan Depth": "扫描深度", "Context %": "上下文百分比", "Budget Cap": "Token预算上限", "(0 = disabled)": "(“0”为禁用)", "Scan chronologically until reached min entries or token budget.": "按时间顺序扫描直到达到最少条目或词符预算。", "Min Activations": "最小激活数", "(disabled when max recursion steps are used)": "（当使用最大递归步数时禁用）", "Max Depth": "最大深度", "(0 = unlimited, use budget)": "（“0”为无限制，使用预算）", "Cap the number of entry activation recursions": "限制条目激活递归的次数", "Max Recursion Steps": "最大递归深度", "0 = unlimited, 1 = scans once and doesn't recurse, 2 = scans once and recurses once, etc": "“0”为无限制，“1”为扫描一次且不递归，“2”为扫描一次且递归一次，依此类推\n（当使用最小激活次数时，此功能被禁用）", "Insertion Strategy": "插入策略", "Sorted Evenly": "均匀排序", "Character Lore First": "角色世界书优先", "Global Lore First": "全局世界书优先", "Include names with each message into the context for scanning": "将每条消息的名称纳入上下文中以供扫描", "Entries can activate other entries by mentioning their keywords": "条目可以通过提及它们的关键字来激活其他条目", "Recursive Scan": "递归扫描", "Lookup for the entry keys in the context will respect the case": "在上下文中查找条目键将继续区分大小写", "Case Sensitive": "区分大小写", "If the entry key consists of only one word, it would not be matched as part of other words": "如果条目键只由一个单词组成，则不会作为其他单词的一部分匹配", "Match Whole Words": "匹配整个单词", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "仅选择具有最多关键匹配项的条目进行包含组过滤", "Use Group Scoring": "使用群组评分", "Alert if your world info is greater than the allocated budget.": "如果您的世界信息大于分配的预算，则会发出警报。", "Alert On Overflow": "溢出警报", "or": "或", "--- Pick to Edit ---": "--- 选择以编辑 ---", "Rename World Info": "重命名世界书", "Open all Entries": "打开所有条目", "Close all Entries": "关闭所有条目", "New Entry": "新条目", "Fill empty Memo/Titles with Keywords": "使用关键字填充空的备忘录/标题", "Apply current sorting as Order": "应用当前排序作为顺序", "Import World Info": "导入世界书", "Export World Info": "导出世界书", "Duplicate World Info": "复制世界书", "Delete World Info": "删除世界书", "Priority": "优先级", "Custom": "自定义", "Title A-Z": "标题 A-Z", "Title Z-A": "标题 Z-A", "Tokens ↗": "词符 ↗", "Tokens ↘": "词符 ↘", "Depth ↗": "深度 ↗", "Depth ↘": "深度 ↘", "Order ↗": "顺序 ↗", "Order ↘": "顺序 ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "触发频率% ↗", "Trigger% ↘": "触发频率% ↘", "Refresh": "刷新", "User Settings": "用户设置", "UI Language": "语言", "Account": "帐户", "Admin Panel": "管理员面板", "Logout": "登出", "Search Settings": "搜索设置", "UI Theme": "UI主题", "Import a theme file": "导入主题文件", "Export a theme file": "导出主题文件", "Delete a theme": "删除主题", "Update a theme file": "更新主题文件", "Save as a new theme": "另存为新主题", "Avatar Style:": "头像样式：", "Circle": "圆形", "Square": "正方形", "Rectangle": "矩形", "Chat Style:": "聊天风格：", "Flat": "扁平", "Bubbles": "气泡", "Document": "文档", "Specify colors for your theme.": "指定您的主题的颜色。", "Theme Colors": "主题颜色", "Main Text": "主要文本", "Italics Text": "斜体文本", "Underlined Text": "下划线文本", "Quote Text": "引用文本", "Shadow Color": "阴影颜色", "Chat Background": "聊天背景", "UI Background": "UI 背景", "UI Border": "UI 边框", "User Message Blur Tint": "用户消息模糊色调", "AI Message Blur Tint": "AI 消息模糊色调", "Chat Width": "页面宽度", "Width of the main chat window in % of screen width": "主聊天窗口的宽度为屏幕宽度的 %", "Font Scale": "字体比例", "Font size": "字体大小", "Blur Strength": "模糊强度", "Blur strength on UI panels.": "UI 面板上的模糊强度。", "Text Shadow Width": "文本阴影宽度", "Strength of the text shadows": "文本阴影的强度", "Disables animations and transitions": "禁用动画和过渡效果", "Reduced Motion": "减少动态效果", "removes blur from window backgrounds": "从窗口背景中移除模糊效果", "No Blur Effect": "禁用模糊效果", "Remove text shadow effect": "移除文本阴影效果", "No Text Shadows": "禁用文本阴影", "Reduce chat height, and put a static sprite behind the chat window": "缩小聊天窗口的高度，并在聊天窗口后面放置一个固定的表情图。", "Waifu Mode": "视觉小说模式", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "始终显示聊天消息的操作菜单完整列表，而不是将它们隐藏在“...”后面", "Auto-Expand Message Actions": "自动展开消息操作菜单", "Alternative UI for numeric sampling parameters with fewer steps": "为数字采样参数提供一个步骤更少的替代用户界面。", "Zen Sliders": "禅意滑块", "Entirely unrestrict all numeric sampling parameters": "完全解除所有数字采样参数的限制", "Mad Lab Mode": "疯狂实验室模式", "Time the AI's message generation, and show the duration in the chat log": "对 AI 生成消息的时间进行计时，并在聊天记录中显示。", "Message Timer": "AI回复计时器", "Show a timestamp for each message in the chat log": "在聊天日志中显示每条消息的时间戳", "Chat Timestamps": "聊天时间戳", "Show an icon for the API that generated the message": "显示此消息所用API的图标", "Model Icon": "模型图标", "Show sequential message numbers in the chat log": "在聊天记录中显示消息楼层", "Message IDs": "显示消息楼层", "Hide avatars in chat messages.": "在聊天记录中隐藏头像。", "Hide Chat Avatars": "隐藏头像", "Show the number of tokens in each message in the chat log": "在聊天记录中显示每条消息的词符数", "Show Message Token Count": "显示消息词符数", "Single-row message input area. Mobile only, no effect on PC": "将输入框限制为一行。仅适用于移动设备，对PC无影响", "Compact Input Area (Mobile)": "紧凑输入区域（移动端）", "Display swipe numbers for all messages, not just the last.": "显示所有信息的滑动编号，而非仅限最后一条。", "Swipe # for All Messages": "给所有信息分配滑动编号 #", "In the Character Management panel, show quick selection buttons for favorited characters": "在角色管理面板中，显示快速选择按钮以选择收藏的角色", "Characters Hotswap": "角色卡热切换", "Enable magnification for zoomed avatar display.": "启用放大功能以放大头像显示。", "Avatar Hover Magnification": "头像悬停放大", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "启用在聊天中点击头像图像后显示放大的头像时的放大效果。", "Show tagged character folders in the character list": "在角色列表中显示已标记的角色文件夹", "Tags as Folders": "标签作为文件夹", "Tags_as_Folders_desc": "最近更改：标签必须在标签管理菜单中标记为文件夹才能显示。单击此处将其调出。", "Character Handling": "角色处理", "If set in the advanced character definitions, this field will be displayed in the characters list.": "如果在高级角色定义中设置，此字段将显示在角色列表中。", "Char List Subheader": "角色列表子标题", "Character Version": "角色版本", "Created by": "创作者", "Defines on importing cards which action should be chosen for importing its listed tags. 'Ask' will always display the dialog.": "定义在导入卡片时应选择哪种操作来导入其列出的标签。“询问”将始终显示对话框。", "Import Card Tags": "导入卡片标签", "Ask": "询问", "tag_import_none": "不导入", "tag_import_all": "导入全部", "tag_import_existing": "仅导入现有的", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "使用模糊匹配，在列表中通过所有数据字段搜索角色，而不仅仅是名称子字符串", "Advanced Character Search": "高级角色搜索", "If checked and the character card contains a prompt override (System Prompt), use that instead": "开启后，如果角色卡已包含系统提示词，则覆盖当前的系统提示词。", "Prefer Character Card Prompt": "角色卡提示词优先", "If checked and the character card contains a Post-History Instructions override, use that instead": "开启后，如果角色卡包含后历史指令覆盖，则使用它。", "Prefer Character Card Instructions": "首选角色卡说明", "never_resize_avatars_tooltip": "避免裁剪和调整导入的角色图像的大小。关闭时，裁剪/调整大小为 512x768。", "Never resize avatars": "永不调整头像大小", "Show actual file names on the disk, in the characters list display only": "在角色列表显示中，显示磁盘上实际的文件名。", "Show avatar filenames": "显示头像文件名", "Hide character definitions from the editor panel behind a spoiler button": "在编辑器面板中，将角色定义隐藏在一个剧透按钮后面。", "Spoiler Free Mode": "防剧透模式", "Reload and redraw the currently open chat": "重新加载并重新渲染当前打开的聊天", "Reload Chat": "重新加载聊天", "Debug Menu": "调试菜单", "Smooth Streaming": "平滑流式传输", "Experimental feature. May not work for all backends.": "实验性功能。可能不适用于所有后端。", "Slow": "慢", "Fast": "快", "Play a sound when a message generation finishes": "当消息生成完毕时播放声音", "Message Sound": "消息声音", "Only play a sound when ST's browser tab is unfocused": "仅在ST的浏览器标签页未被打开时播放声音", "Background Sound Only": "仅背景声音", "Reduce the formatting requirements on API URLs": "减少API URL的格式化要求", "Relaxed API URLS": "宽松的API URL", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "询问是否为每个具有嵌入的知识书的新角色导入世界信息/知识书。如果未选中，则会显示简短的消息", "Lorebook Import Dialog": "知识书导入对话框", "Enable auto-select of input text in some text fields when clicking/selecting them. Applies to popup input textboxes, and possible other custom input fields.": "启用在某些文本字段中单击/选择时自动选中文本的功能。适用于弹出输入框以及可能的其他自定义输入字段。", "Auto-select Input Text": "自动选择输入文本", "markdown_hotkeys_desc": "在特定文本输入框中，启用插入 Markdown 格式的快捷键。详情输入：“/help hotkeys”。", "Markdown Hotkeys": "Markdown 快捷键", "Restore unsaved user input on page refresh": "在页面刷新时恢复未保存的用户输入", "Restore User Input": "恢复用户输入", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "允许通过拖动重新定位某些UI元素。仅适用于PC，对移动设备无影响", "Movable UI Panels": "可移动 UI 面板", "Reset MovingUI panel sizes/locations.": "重置 可移动UI 面板大小/位置。", "mui_reset": "Reset", "MovingUI preset. Predefined/saved draggable positions": "可移动UI预设。预定义/保存的可拖动位置", "MUI Preset": "可移动 UI 预设", "Save movingUI changes to a new file": "将可移动UI更改保存到新文件中", "Apply a custom CSS style to all of the ST GUI": "将自定义CSS样式应用于所有ST GUI", "Custom CSS": "自定义 CSS", "Chat/Message Handling": "聊天/消息处理", "# Messages to Load": "要加载 # 条消息", "The number of chat history messages to load before pagination.": "分页前要加载的聊天历史消息数。", "(0 = All)": "（“0”为全部）", "Streaming FPS": "流式传输帧速率", "Update speed of streamed text.": "文本流的更新速度。", "Example Messages Behavior": "示例消息行为", "Gradual push-out": "逐渐推出", "Always include examples": "始终包含示例", "Never include examples": "永不包含示例", "Send on Enter": "按 Enter 发送", "Disabled": "已禁用", "Automatic (PC)": "自动（PC）", "Enabled": "已启用", "Press Send to continue": "按发送键以继续", "Show a button in the input area to ask the AI to continue (extend) its last message": "在输入区域中显示一个按钮，要求AI继续（延长）其上一条消息", "Quick 'Continue' button": "快速“继续”按钮", "Show a button in the input area to ask the AI to impersonate your character for a single message": "在输入区域中显示一个按钮，让 AI 模仿你的角色发送一条消息。", "Quick 'Impersonate' button": "快速“模仿”按钮", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "在聊天窗口的最后一条信息上显示箭头按钮，以生成AI的其他回复选项。适用于电脑和手机端。", "Swipes": "刷新回复按钮", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "允许在最后一条聊天消息上使用滑动手势触发滑动生成。仅适用于移动设备，对PC无影响", "Gestures": "手势", "Auto-load Last Chat": "自动加载上次聊天", "Auto-scroll Chat": "自动滚动聊天", "Save edits to messages without confirmation as you type": "在键入时保存对消息的编辑而无需确认", "Auto-save Message Edits": "自动保存消息编辑", "Confirm message deletion": "删除消息前确认", "Auto-fix Markdown": "自动修复 Markdown", "Disallow embedded media from other domains in chat messages": "禁止在聊天消息中嵌入来自其他域的媒体。", "Forbid External Media": "禁止外部媒体", "Allow {{char}}: in bot messages": "在机器人消息中允许 {{char}}: ", "Allow {{user}}: in bot messages": "在机器人消息中允许 {{user}}: ", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "跳过消息文本中的编码和字符，允许一部分HTML标记以及Markdown", "Show tags in responses": "在响应中显示标签", "Allow AI messages in groups to contain lines spoken by other group members": "允许群聊中的AI输出群中其他成员说的话", "Relax message trim in Groups": "减轻群聊中的消息修剪", "Log prompts to console": "将提示词输出到控制台", "Requests logprobs from the API for the Token Probabilities feature": "从API请求对数概率数据，用于实现词符概率功能。", "Request token probabilities": "请求词符概率", "In group chat, highlight the character(s) that are currently queued to generate responses and the order in which they will respond.": "在群聊中，突出显示当前排队等待生成响应的角色以及他们响应的顺序。", "Show group chat queue": "显示群聊队列", "Automatically reject and re-generate AI message based on configurable criteria": "根据可配置的条件自动拒绝并重新生成AI消息", "Auto-swipe": "自动滑动", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "启用自动滑动功能。仅当启用自动滑动时，本节中的设置才会生效", "Minimum generated message length": "生成的消息的最小长度", "If the generated message is shorter than these many characters, trigger an auto-swipe": "如果生成的消息短于此长度，则触发自动滑动", "Blacklisted words": "屏蔽词", "words you dont want generated separated by comma ','": "不想生成的词语，用半角逗号“,”分隔", "Blacklisted word count to swipe": "触发滑动的黑名单词语数量", "Minimum number of blacklisted words detected to trigger an auto-swipe": "触发自动滑动刷新回复所需检测到的最少违禁词数量。", "Automatically 'continue' a response if the model stopped before reaching a certain amount of tokens.": "当回复没有达到特定词符数时，自动让模型“继续”这个回复。", "Auto-Continue": "自动继续", "Allow for Chat Completion APIs": "允许使用聊天补全API", "Target length (tokens)": "目标长度（以词符数计）", "AutoComplete Settings": "自动补全设置", "Automatically hide details": "自动隐藏详细信息", "Determines how entries are found for autocomplete.": "确定如何找到自动补全的条目。", "Autocomplete Matching": "匹配", "Starts with": "以此开始：", "Includes": "包括", "Fuzzy": "模糊", "Sets the style of the autocomplete.": "设置自动完成的样式。", "Autocomplete Style": "风格", "Follow Theme": "关注主题", "Dark": "黑暗的", "Keyboard": "键盘：", "Select with Tab or Enter": "使用 Tab 或 Enter 选择", "Select with Tab": "使用 Tab 选择", "Select with Enter": "按 Enter 键选择", "Sets the font size of the autocomplete.": "设置自动完成的字体大小。", "Sets the width of the autocomplete.": "设置自动完成的宽度。", "Autocomplete Width": "宽度", "chat input box": "聊天输入框", "entire chat width": "整个聊天宽度", "full window width": "全窗口宽度", "STscript Settings": "STscript设置", "Sets default flags for the STscript parser.": "为 STscript 解析器设置默认标志。", "Parser Flags": "解析器标志", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "切换到更严格的转义，允许所有分隔字符用反斜杠转义，并且反斜杠也可以转义。", "STRICT_ESCAPING": "严格转义", "stscript_parser_flag_replace_getvar_label": "防止 {{getvar::}} {{getglobalvar::}} 宏具有自动评估的文字宏类值。\n例如，“{{newline}}”保留为文字字符串“{{newline}}”\n\n（这是通过在内部用范围变量替换 {{getvar::}} {{getglobalvar::}} 宏来实现的。）", "REPLACE_GETVAR": "替换GETVAR", "Change Background Image": "更改背景图片", "Background Image": "背景图片", "Filter": "搜索", "Background Fitting": "背景图片尺寸", "Classic": "经典", "Cover": "填充", "Contain": "不变换", "Stretch": "拉伸", "Center": "居中", "Automatically select a background based on the chat context": "根据聊天上下文自动选择背景", "Auto-select": "自动选择", "System Backgrounds": "系统背景", "Chat Backgrounds": "聊天背景", "bg_chat_hint_1": "使用生成的聊天背景", "bg_chat_hint_2": "扩展名将出现在这里。", "Extensions": "扩展", "Notify on extension updates": "在扩展更新时通知", "Manage extensions": "管理扩展", "Import Extension From Git Repo": "从Git存储库导入扩展", "Install extension": "安装扩展", "Extras API:": "扩展API：", "Auto-connect": "自动连接", "Extras API URL": "附加 API URL", "Extras API key (optional)": "扩展API密钥（可选）", "Persona Management": "用户角色管理", "Click for stats!": "点击查看统计！", "Usage Stats": "使用统计", "Backup your personas to a file": "将用户角色备份到文件中", "Backup": "备份", "Restore your personas from a file": "从文件中恢复用户角色", "Restore": "恢复", "Create a dummy persona": "创建空白用户角色", "Create": "创建", "No persona description": "[没有描述]", "Name": "名称", "Enter your name": "输入您的名字", "Click to set a new User Name": "点击设置新的用户名", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "单击以将您选择的用户角色锁定到当前聊天。再次单击以移除锁定。", "Click to set user name for all messages": "点击为所有消息设置用户名", "Persona Lore Alt+Click to open the lorebook": "Persona Lore\nAlt+Click to open the lorebook", "Persona Description": "用户角色描述", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "示例：[{{user}}是一个28岁的罗马尼亚猫娘。]", "Tokens persona description": "用户角色描述词符数", "Position:": "位置：", "None (disabled)": "无（已禁用）", "In Story String / Prompt Manager": "在故事字符串/提示词管理器中", "Top of Author's Note": "作者注的顶部", "Bottom of Author's Note": "作者注的底部", "In-chat @ Depth": "聊天的特定深度", "Depth:": "深度：", "Role:": "身份：", "System": "系统", "User": "用户", "Assistant": "助手", "Show notifications on switching personas": "切换用户角色时显示通知", "Character Management": "角色管理", "Locked = Character Management panel will stay open": "锁定 = 角色管理面板将保持打开状态", "Select/Create Characters": "选择/创建角色", "Favorite characters to add them to HotSwaps": "收藏角色以将它们添加到快速热切换区", "Token counts may be inaccurate and provided just for reference.": "词符计数可能不准确，仅供参考。", "Total tokens": "总词符数", "Calculating...": "正在计算...", "Tokens": "词符数", "Permanent tokens": "永久词符", "Permanent": "恒定的", "About Token 'Limits'": "关于词符“限制”", "Toggle character info panel": "切换角色信息面板", "Name this character": "为这个角色命名", "extension_token_counter": "词符：", "Click to select a new avatar for this character": "单击以为此角色选择新的头像", "Add to Favorites": "添加到收藏夹", "Advanced Definition": "高级定义", "world_button_title": "Character Lore\n\nClick to load\nShift-click to open 'Link to World Info' popup", "Chat Lore Alt+Click to open the lorebook": "Chat Lore\nAlt+Click to open the lorebook", "Export and Download": "导出并下载", "Duplicate Character": "复制角色", "Create Character": "创建角色", "Delete Character": "删除角色", "More...": "更多...", "Link to World Info": "链接到世界书", "Import Card Lore": "导入角色卡的世界书", "Scenario Override": "场景覆盖", "Convert to Persona": "转换为用户角色", "Rename": "重命名", "Link to Source": "来源链接", "Replace / Update": "替换 / 更新", "Import Tags": "导入标签", "Search / Create Tags": "搜索/创建标签", "View all tags": "查看所有标签", "Creator's Notes": "创作者的注释", "Character details are hidden.": "角色详情已隐藏。", "Show / Hide Description and First Message": "显示/隐藏描述和第一条消息", "Character Description": "角色描述", "Click to allow/forbid the use of external media for this character.": "单击以允许/禁止此角色使用外部媒体。", "Ext. Media": "扩展媒体", "Describe your character's physical and mental traits here.": "在这里描述您角色的身体和精神特征。", "First message": "第一条消息", "Click to set additional greeting messages": "单击以设置其他问候消息", "Alt. Greetings": "其他开场", "This will be the first message from the character that starts every chat.": "这将是角色在每次聊天开始时发送的第一条消息。", "Group Controls": "群聊控制", "Chat Name (Optional)": "聊天名称（可选）", "Chat Lore": "聊天知识", "Click to select a new avatar for this group": "单击选择该群聊的新头像", "Group reply strategy": "群聊发言顺序", "Manual": "手动", "Natural order": "自然顺序", "List order": "从上到下", "Group generation handling mode": "群组生成处理模式", "Swap character cards": "交换角色卡", "Join character cards (exclude muted)": "加入角色卡（不包括被禁言的）", "Join character cards (include muted)": "加入角色卡（包括被禁言的）", "Inserted before each part of the joined fields.": "插入到加入字段的每个部分之前。", "Join Prefix": "加入前缀", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "当选择“合并角色卡”时，角色的所有相应字段将被合并在一起。这意味着在故事字符串中，例如，所有角色描述都将合并为一个大文本。如果您希望将这些字段分开，可以在此处定义前缀或后缀。此值支持普通宏，还会将 {{char}} 替换为相关角色的名称，将 <FIELDNAME> 替换为部分的名称（例如：描述、个性、场景等）", "Inserted after each part of the joined fields.": "插入到加入字段的每个部分之后。", "Join Suffix": "加入后缀", "Set a group chat scenario": "设置群聊背景", "Click to allow/forbid the use of external media for this group.": "单击以允许/禁止该组使用外部媒体。", "Restore collage avatar": "恢复拼贴头像", "Allow self responses": "允许自我回复", "Auto Mode": "自动模式", "Auto Mode delay": "自动模式延迟", "Hide Muted Member Sprites": "隐藏拼贴头像中被禁言的成员", "Current Members": "当前成员", "Add Members": "添加成员", "Create New Character": "新建角色", "Import Character from File": "从文件导入角色", "Import content from external URL": "从外部URL导入内容", "Create New Chat Group": "创建新的群聊", "Characters sorting order": "角色排序顺序", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "最新", "Oldest": "最旧", "Favorites": "收藏夹", "Recent": "最近", "Most chats": "最多聊天", "Least chats": "最少聊天", "Most tokens": "最多词符", "Least tokens": "最少词符", "Random": "随机", "Toggle character grid view": "切换角色网格视图", "Bulk_edit_characters": "批量编辑角色", "Bulk select all characters": "批量选择所有角色", "Bulk delete characters": "批量删除角色", "Bind user name to that avatar": "将用户名称绑定到该头像", "Change persona image": "更改用户角色头像", "Select this as default persona for the new chats.": "选择此项作为新聊天的默认用户角色。", "Duplicate persona": "复制用户角色", "Delete persona": "删除用户角色", "popup-button-save": "保存", "popup-button-yes": "是", "popup-button-no": "否", "popup-button-cancel": "取消", "popup-button-import": "导入", "popup-button-crop": "裁剪", "Close popup": "关闭弹出窗口", "Advanced Definitions": "高级定义", "Prompt Overrides": "提示词覆盖", "(For Chat Completion and Instruct Mode)": "（用于聊天补全和指导模式）", "Insert {{original}} into either box to include the respective default prompt from system settings.": "将{{original}}插入到任一框中，以包含系统设置中的相应默认提示词。", "Main Prompt": "主要提示词", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "此处的任何内容都将替换用于此角色的默认主提示词。（v2规范：system_prompt）", "Any contents here will replace the default Post-History Instructions used for this character. (v2 spec: post_history_instructions)": "此处的任何内容都将替换此角色使用的默认后历史说明。\n（v2 规范：post_history_instructions）", "Creator's Metadata (Not sent with the AI prompt)": "创作者的元数据（不与AI提示词一起发送）", "Creator's Metadata": "创作者的元数据", "(Not sent with the AI Prompt)": "（不随 AI 提示词发送）", "Everything here is optional": "这里的一切都是可选的", "(Botmaker's name / Contact Info)": "（角色制作者的姓名/联系信息）", "(If you want to track character versions)": "（如果您想跟踪角色版本）", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "（描述角色，提供使用技巧，或列出已经测试过的聊天模型。这将显示在角色列表中。）", "Tags to Embed": "嵌入的标签", "(Write a comma-separated list of tags)": "（编写一个以逗号分隔的标签列表。）", "Personality summary": "角色设定摘要", "(A brief description of the personality)": "（角色设定的简要描述）", "Scenario": "情景", "(Circumstances and context of the interaction)": "（交互的情况和背景）", "Character's Note": "角色备注", "(Text to be inserted in-chat @ designated depth and role)": "（文本将插入聊天中指定的深度和角色）", "@ Depth": "@ 深度", "Role": "角色", "Talkativeness": "发言频率", "How often the character speaks in group chats!": "角色在群聊中发言的频率！", "How often the character speaks in": "角色发言的频率", "group chats!": "群聊中！", "Shy": "低", "Normal": "正常", "Chatty": "高", "Examples of dialogue": "对话示例", "Important to set the character's writing style.": "设置角色的写作风格，很重要！", "(Examples of chat dialog. Begin each example with START on a new line.)": "（聊天对话的示例，每个示例都另起一行以<START>开头。）", "Save": "保存", "Chat History": "聊天记录", "Import Chat": "导入聊天", "Copy to system backgrounds": "复制到系统背景", "Rename background": "重命名背景", "Lock": "锁定", "Unlock": "解锁", "Delete background": "删除背景", "Select a World Info file for": "选择一个世界书文件给", "Primary Lorebook": "主要知识书", "A selected World Info will be bound to this character as its own Lorebook.": "所选的世界信息将会于该角色绑定，作为该角色自己的知识书", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "在生成AI回复时，它将与全局世界信息选择器中的条目相结合。", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "导出角色还将导出嵌入在JSON数据中的所选知识书文件。", "Additional Lorebooks": "附加知识书", "Associate one or more auxillary Lorebooks with this character.": "将一个或多个辅助知识书与此角色关联。", "NOTE: These choices are optional and won't be preserved on character export!": "注意：这些选项是可选的，并且不会在角色导出时被一并导出！", "Rename chat file": "重命名聊天文件", "Export JSONL chat file": "导出 .JSONL 聊天文件", "Download chat as plain text document": "将聊天下载为纯文本文档", "Delete chat file": "删除聊天文件", "Drag to reorder tag": "拖动以排序", "Use tag as folder": "标记为文件夹", "Hide on character card": "在角色卡上隐藏", "Delete tag": "删除标签", "Toggle entry's active state.": "切换条目激活状态。", "Entry Title/Memo": "条目标题/备忘录", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized": "世界书条目状态：\r🔵 永久\r🟢 关键词\r🔗 向量化", "WI_Entry_Status_Constant": "永久", "WI_Entry_Status_Normal": "关键词", "WI_Entry_Status_Vectorized": "向量化", "T_Position": "↑Char：在角色定义之前\n↓Char：在角色定义之后\n↑AN：在作者注释之前\n↓AN：在作者注释之后\n@D：在深度D处", "Before Char Defs": "角色定义之前", "After Char Defs": "角色定义之后", "Before EM": "↑EM", "After EM": "↓EM", "Before AN": "作者注释之前", "After AN": "作者注释之后", "at Depth System": "@D ⚙​​️", "at Depth User": "@D 👤", "at Depth AI": "@D 🤖", "Depth": "深度", "Order:": "顺序：", "Order": "顺序", "Trigger %:": "触发 %：", "Duplicate world info entry": "重复的世界信息条目", "Delete world info entry": "删除世界信息条目", "Comma separated (required)": "逗号分隔（必填）", "Primary Keywords": "主要关键字", "Keywords or Regexes": "关键字或正则表达式", "Comma separated list": "逗号分隔列表", "Switch to plaintext mode": "切换到纯文本模式", "Logic": "逻辑", "AND ANY": "与任意", "AND ALL": "与所有", "NOT ALL": "非所有", "NOT ANY": "非任何", "(ignored if empty)": "（若为空则忽略）", "Optional Filter": "可选过滤器", "Keywords or Regexes (ignored if empty)": "关键字或正则表达式（如果为空则忽略）", "Comma separated list (ignored if empty)": "逗号分隔列表（如果为空则忽略）", "Use global setting": "使用全局设置", "Case-Sensitive": "区分大小写", "Use global": "使用全局", "Yes": "是", "No": "否", "Whole Words": "Whole Words", "Group Scoring": "Group Scoring", "Can be used to automatically activate Quick Replies": "可用于自动激活快速回复", "Automation ID": "自动化ID", "( None )": "（没有任何）", "delay_until_recursion_level": "Defines delay levels for recursive scans.\r\rInitially, only the first level (smallest number) will match.\rOnce no matches are found, the next level becomes eligible for matching.\rThis repeats until all levels are checked.\r\rTied to the \"Delay until recursion\" setting.", "Recursion Level": "递归等级", "Content": "内容", "Non-recursable (will not be activated by another)": "不可递归（不会被其他条目激活）", "Prevent further recursion (this entry will not activate others)": "防止进一步递归（本条目将不会激活其他条目）", "Delay until recursion (can only be activated on recursive checking)": "延迟到递归（本条目只能在递归检查时激活）", "What this keyword should mean to the AI, sent verbatim": "这个关键词对AI的含义，逐字发送", "Inclusion Group": "包含组", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "包含组可确保每次仅激活组中的一项（如果触发了多项）。支持多个逗号分隔的组。文档：世界信息 - 包含组", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "优先考虑此条目：选中后，此条目在所有选择中优先考虑。如果优先考虑多个条目，则选择“顺序”最高的条目。", "Prioritize": "确定优先级", "Only one entry with the same label will be activated": "只有一个带有相同标签的条目将被激活", "A relative likelihood of entry activation within the group": "组内进入激活的相对可能性", "Group Weight": "组权重", "Sticky entries will stay active for N messages after being triggered.": "粘性条目在被触发后将保持活跃状态​​ N 条消息。", "Sticky": "黏性", "Non-sticky": "无黏性", "Entries with a cooldown can't be activated N messages after being triggered.": "具有冷却时间的条目在触发后 N 条消息内无法被激活。", "Cooldown": "冷却", "No cooldown": "无冷却", "Entries with a delay can't be activated until there are N messages present in the chat.": "直到聊天中出现 N 条消息时，延迟的条目才能被激活。", "Delay": "延迟", "No delay": "无延迟", "Filter to Characters or Tags": "绑定到角色或标签", "Switch the Character/Tags filter around to exclude the listed characters and tags from matching for this entry": "切换角色/标签筛选方式，将列出的角色和标签排除在匹配范围之外", "Exclude": "排除", "-- Characters not found --": "-- 未找到角色 --", "Selective": "选择性", "Use Probability": "使用概率", "Add Memo": "添加备忘录", "Text or token ids": "文本或 [token ID]", "Type here...": "在此处输入...", "close": "关闭", "prompt_manager_edit": "编辑", "prompt_manager_name": "姓名", "A name for this prompt.": "此提示词的名称。", "To whom this message will be attributed.": "此消息应归于谁。", "AI Assistant": "AI助手", "prompt_manager_position": "位置", "Relative (to other prompts in prompt manager) or In-chat @ Depth.": "相对（相对于提示管理器中的其他提示）或在聊天中@深度。", "prompt_manager_relative": "相对", "prompt_manager_in_chat": "聊天中", "prompt_manager_depth": "深度", "0 = after the last message, 1 = before the last message, etc.": "“0”为在最后一条消息之后，“1”为在最后一条消息之前，等等。", "The content of this prompt is pulled from elsewhere and cannot be edited here.": "此提示词的内容是从其他地方提取的，无法在此处进行编辑。", "Prompt": "提示词", "The prompt to be sent.": "要发送的提示词。", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "即使选择覆盖，此提示词也不能被角色卡覆盖。", "prompt_manager_forbid_overrides": "禁止覆盖", "reset": "重置", "save": "保存", "This message is invisible for the AI": "此消息对AI不可见", "Message Actions": "消息操作", "Translate message": "翻译消息", "Generate Image": "生成图片", "Narrate": "朗读", "Exclude message from prompts": "从提示词中排除消息", "Include message in prompts": "将消息包含在提示词中", "Embed file or image": "嵌入文件或图像", "Create checkpoint": "创建检查点", "Create Branch": "创建分支", "Copy": "复制", "Open checkpoint chat\nShift+Click to replace the existing checkpoint with a new one": "点击打开检查点聊天\nShift+单击来覆盖已有检查点", "Edit": "编辑", "Confirm": "确认", "Copy this message": "复制此消息", "Add a reasoning block": "添加一个推理块", "Delete this message": "删除此消息", "Move message up": "将消息上移", "Move message down": "将消息下移", "Thought for some time": "思考了一会", "Confirm Edit": "确认", "Remove reasoning": "删除推理内容", "Cancel edit": "Cancel edit", "Copy reasoning": "复制推理内容", "Edit reasoning": "编辑推理内容", "Enlarge": "放大", "Caption": "标题", "Swipe left": "<PERSON>wipe left", "Swipe right": "Swipe right", "Welcome to SillyTavern!": "欢迎来到 SillyTavern！", "SillyTavern is aimed at advanced users.": "SillyTavern 面向高级用户。", "welcome_message_part_1": "阅读", "welcome_message_part_2": "官方文档", "welcome_message_part_3": "。", "welcome_message_part_4": "类型", "welcome_message_part_5": "在聊天中输入命令和宏。", "welcome_message_part_6": "加入", "Discord server": "Discord 服务器", "welcome_message_part_7": "了解信息和公告。", "Looking for AI characters?": "正在寻找 AI 角色？", "onboarding_import": "导入", "from supported sources or view": "来自受支持的来源或查看", "Sample characters": "示例角色", "Your Persona": "您的用户角色", "Before you get started, you must select a persona name.": "在开始之前，您必须选择一个用户角色名称。", "welcome_message_part_8": "您可随时通过", "welcome_message_part_9": "图标来更改此设置。", "Persona Name:": "用户角色名称：", "Temporarily disable automatic replies from this character": "临时禁言此角色", "Enable automatic replies from this character": "解除禁言此角色", "Trigger a message from this character": "强制触发该角色发言", "Move up": "向上移动", "Move down": "向下移动", "View character card": "查看角色卡片", "Remove from group": "踢出群聊", "Add to group": "拉入群聊", "Alternate Greetings": "额外问候语", "Alternate_Greetings_desc": "开始新聊天时，这些按钮将显示为第一条消息的滑动选项。\n群成员可以选择其中之一来发起对话。", "alternate_greetings_hint_1": "点击", "alternate_greetings_hint_2": "按钮即可开始！", "Alternate Greeting #": "额外问候语 #", "(This will be the first message from the character that starts every chat)": "（这是每次聊天开始时角色的第一条消息）", "View contents": "查看内容", "Remove the file": "删除文件", "Author's Note": "作者注释", "Unique to this chat": "仅对此聊天生效", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "检查点从其父级继承注释，之后可以单独更改。", "Include in World Info Scanning": "纳入世界信息扫描", "Before Main Prompt / Story String": "主提示词/故事线之前", "After Main Prompt / Story String": "主提示词/故事线之后", "as": "作为", "Insertion Frequency": "插入频率", "(0 = Disable, 1 = Always)": "（“0”为禁用，“1”为始终）", "User inputs until next insertion:": "用户输入直到下一次插入：", "Character Author's Note (Private)": "人物作者注（私密）", "Won't be shared with the character card on export.": "导出时不会与角色卡共享。", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "会自动添加为该角色的作者注解，在群聊中使用，但群聊开启时无法修改。", "Use character author's note": "使用角色作者的注释", "Replace Author's Note": "替换作者注", "Default Author's Note": "默认作者注", "Will be automatically added as the Author's Note for all new chats.": "将自动添加为所有新聊天的作者注释。", "Chat CFG": "本聊天的CFG缩放", "1 = disabled": "“1”为禁用", "write short replies, write replies using past tense": "写简短的回复，用过去时写回复", "Positive Prompt": "正面提示词", "Use character CFG scales": "单独为各个角色设置CFG缩放", "Character CFG": "角色CFG配置", "Will be automatically added as the CFG for this character.": "将自动添加到该角色的CFG设置中。", "Global CFG": "全局CFG", "Will be used as the default CFG options for every chat unless overridden.": "除非被覆盖，否则将用作每次聊天的默认 CFG 选项。", "CFG Prompt Cascading": "CFG 提示词级联", "Combine positive/negative prompts from other boxes.": "结合来自其他框的正面/负面提示词。", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "例如，勾选聊天、全局和角色框会将所有负面提示词组合成以逗号分隔的字符串。", "Always Include": "始终包含", "Chat Negatives": "聊天负面信息", "Character Negatives": "性格缺点", "Global Negatives": "全局负面信息", "Custom Separator:": "自定义分隔符：", "Insertion Depth:": "插入深度：", "Token Probabilities": "词符概率", "Select a token to see alternatives considered by the AI.": "选择一个词符来查看 AI 考虑的替代方案。", "Reroll with the entire prefix": "使用完整前缀重新生成", "Not connected to API!": "未连接到API！", "Type a message, or /? for help": "输入想发送的消息，或输入 /? 获取帮助", "Continue script execution": "继续执行脚本", "Pause script execution": "暂停执行脚本", "Abort script execution": "中止执行脚本", "Abort request": "中止请求", "Ask AI to write your message for you": "让AI为您撰写消息", "Continue the last message": "继续上一条消息", "Send a message": "发送消息", "Close chat": "关闭聊天", "Toggle Panels": "切换面板", "CFG Scale": "CFG缩放", "Back to parent chat": "返回到父级聊天", "Save checkpoint": "保存检查点", "Convert to group": "转换为群聊", "Start new chat": "开始新聊天", "Manage chat files": "管理聊天文件", "Delete messages": "删除消息", "Regenerate": "重新生成", "Impersonate": "AI 帮答", "Continue": "继续", "extension_install_1": "若想从此页安装扩展程序，你需要提前安装", "extension_install_2": "。", "extension_install_3": "点这个图标（", "extension_install_4": "）前往扩展程序的代码仓库以了解如何使用它。", "These characters are the winners of character design contests and have outstandable quality.": "这些角色都是角色设计大赛的获奖者，品质非常出色。", "Contest Winners": "比赛获胜者", "These characters are the finalists of character design contests and have remarkable quality.": "这些角色都是角色设计大赛的入围作品，品质十分出色。", "Featured Characters": "特色角色", "Download Extensions & Assets": "下载扩展和资源菜单", "Load a custom asset list or select": "加载自定义资产列表或选择", "to install 3rd party extensions.": "安装第三方扩展。", "Assets URL": "资产网址", "load_asset_list_desc": "根据资产列表文件加载扩展和资产列表。\n\n此字段中的默认资产 URL 指向官方第一方扩展和资产列表。\n如果您有自定义资产列表，可以在此处插入。\n\n要安装单个第三方扩展，请使用右上角的“安装扩展”按钮。", "Load an asset list": "加载资产列表", "Load Asset List": "加载资产列表", "Characters": "人物", "Attach a File": "附加文件", "Enter a URL or the ID of a Fandom wiki page to scrape:": "输入要抓取的 Fandom wiki 页面的 URL 或 ID：", "Examples:": "例：", "Example:": "例：", "Single file": "单个文件", "All articles will be concatenated into a single file.": "所有文章将被合并为一个文件。", "File per article": "每篇文章的文件数", "Each article will be saved as a separate file.": "不推荐。每篇文章将保存为单独的文件。", "Open Data Bank": "打开数据库", "Data Bank": "数据库", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "这些文件将可用于支持附件的扩展（例如 Vector Storage）。", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "支持的文件类型：纯文本、PDF、Markdown、HTML、EPUB。", "Drag and drop files here to upload.": "将文件拖放到此处进行上传。", "Date (Newest First)": "日期（最新日期）", "Date (Oldest First)": "日期（最早日期）", "Name (A-Z)": "姓名（从 A 到 Z）", "Name (Z-A)": "姓名 (Z-A)", "Size (Smallest First)": "尺寸（最小）", "Size (Largest First)": "尺寸（最大尺寸优先）", "Bulk Edit": "批量编辑", "Select All": "全选", "Select None": "清空选择", "Disable": "禁用", "Enable": "启用", "Global Attachments": "全局附件", "These files are available for all characters in all chats.": "这些文件可供所有聊天中的所有角色使用。", "Character Attachments": "角色附件", "These files are available for the current character in all chats they are in.": "当前角色可以在其所在的所有聊天中使用这些文件。", "Saved locally. Not exported.": "已本地保存。未导出。", "Chat Attachments": "聊天附件", "These files are available for all characters in the current chat.": "这些文件可供当前聊天中的所有角色使用。", "Enter a base URL of the MediaWiki to scrape.": "输入要抓取的 MediaWiki 的基本 URL。", "Don't include the page name!": "不要包含页面名称！", "Enter web URLs to scrape (one per line):": "输入要抓取的网址（每行一个）：", "Enter a video URL to download its transcript.": "输入视频 URL 或 ID 即可下载其文本。", "Image Captioning": "图像描述", "Source": "来源", "Local": "本地", "Multimodal (OpenAI / Anthropic / llama / Google)": "多模态（OpenAI / Anthropic / llama / Google）", "Extras": "更多", "Horde": "Horde", "API": "API", "Text Generation WebUI (oobabooga)": "文本生成 WebUI (oobabooga)", "Model": "模型", "currently_selected": "[当前选定]", "currently_loaded": "[当前正在加载]", "Allow reverse proxy": "允许反向代理", "Hint:": "提示：", "Set your API keys and endpoints in the 'API Connections' tab first.": "首先在“API 连接”选项卡中设置您的 API 密钥和端点。", "Caption Prompt": "图像描述提示词", "Ask every time": "每次都询问", "Message Template": "消息模板", "(use _space": "（使用", "macro)": "宏指令）", "Automatically caption images": "自动为图像添加标题", "Edit captions before saving": "保存前编辑标题", "Included settings:": "包含的设置：", "{{@key}}": "{{@key}}:", "Profile name:": "配置名称：", "Creating a Connection Profile": "新建API连接配置", "Click on the setting name to omit it from the profile.": "点击设置名称以将其从连接配置中删除。", "Enter a name:": "输入名字：", "Connection Profile": "API连接配置", "View connection profile details": "查看API连接配置详情", "Create a new connection profile": "新建一个API连接配置", "Update a connection profile": "更新API连接配置", "Edit a connection profile": "编辑API连接配置", "Reload a connection profile": "重载API连接配置", "Delete a connection profile": "删除API连接配置", "Omitted Settings:": "排除的设置：", "Character Expressions": "角色表情", "Use the selected API from Chat Translation extension settings.": "使用聊天翻译扩展程序中已选择的API。", "Translate text to English before classification": "分类之前将文本翻译成英文", "A single expression can have multiple sprites. Whenever the expression is chosen, a random sprite for this expression will be selected.": "A single expression can have multiple sprites. Whenever the expression is chosen, a random sprite for this expression will be selected.", "Allow multiple sprites per expression": "Allow multiple sprites per expression", "If the same expression is used again, re-roll the sprite. This only applies to expressions that have multiple available sprites assigned.": "If the same expression is used again, re-roll the sprite. This only applies to expressions that have multiple available sprites assigned.", "Re-roll if same expression is used again": "Re-roll if same sprite is used again", "Classifier API": "分类器 API", "Select the API for classifying expressions.": "选择用于对表达式进行分类的API。", "Main API": "当前连接的 API", "WebLLM Extension": "WebLLM Extension", "LLM Prompt": "大语言模型提示词", "Will be used if the API doesn't support JSON schemas or function calling.": "如果 API 不支持 JSON 模式或函数调用，则会使用它。", "Default / Fallback Expression": "默认/后备表达式", "Set the default and fallback expression being used when no matching expression is found.": "设置在未找到匹配表达式时使用的默认表达式和后备表达式。", "Custom Expressions": "自定义表达式", "Can be set manually or with an _space": "可以手动设置或使用", "space_ slash command.": "快捷命令来设置。", "Open a chat to see the character expressions.": "打开聊天即可查看人物表情。", "You are in offline mode. Click on the image below to set the expression.": "您处于离线模式。点击下方图片即可设置表情。", "Sprite Folder Override": "表情文件夹覆盖", "Use a forward slash to specify a subfolder. Example: _space": "使用正斜杠指定子文件夹。例如：", "Upload sprite pack (ZIP)": "上传表情包（ZIP）", "Remove all image overrides": "删除所有图片覆盖", "Create new folder in the _space": "创建新文件夹到", "folder of your user data directory and name it as the name of the character.": "用户数据目录的文件夹并将其命名为角色的名称。", "Put images with expressions there. File names should follow the pattern:": "将带有表情的图像放在那里。文件名应遵循以下模式：", "expression_label_pattern": "[表达式标签].[图像格式]", "Sprite set:": "表情集：", "upload_expression_request": "请输入表情名称（不用加后缀）。", "upload_expression_naming_1": "素材名称必须符合所选表情 {{expression}} 的命名规范", "upload_expression_naming_2": "当存在多个表情时，名称应由表情名称与合法后缀构成，允许使用横杠'-'或英文句号'.'作为分隔符。", "upload_expression_replace": "点击“替换”以替换当前表情：", "Show Gallery": "展示图库", "ext_sum_title": "总结", "ext_sum_with": "总结如下：", "ext_sum_main_api": "主要 API", "ext_sum_webllm": "WebLLM 扩展", "ext_sum_current_summary": "当前摘要：", "ext_sum_restore_tip": "恢复先前的摘要；重复使用以清除此聊天的摘要状态", "ext_sum_restore_previous": "恢复上一个", "ext_sum_memory_placeholder": "摘要将在这里生成...", "ext_sum_force_tip": "立即触发摘要更新。", "ext_sum_force_text": "现在总结", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "禁用自动摘要更新。暂停时，摘要保持原样。您仍然可以通过按“立即汇总”按钮（仅适用于主 API）强制更新。", "ext_sum_pause": "暂停", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "从要总结的文本中省略世界信息和作者注释。仅在使用主 API 时有效。附加 API 始终省略世界书/作者注。", "ext_sum_no_wi_an": "无世界书/作者注", "ext_sum_settings_tip": "编辑摘要提示词、插入位置等。", "ext_sum_settings": "摘要设置", "ext_sum_prompt_builder": "提示词生成器", "ext_sum_prompt_builder_1_desc": "扩展将使用尚未汇总的消息构建自己的提示词。阻止聊天，直到生成摘要为止。", "ext_sum_prompt_builder_1": "原始，阻塞", "ext_sum_prompt_builder_2_desc": "扩展将使用尚未汇总的消息构建自己的提示词。在生成摘要时不会阻止聊天。并非所有后端都支持此模式。", "ext_sum_prompt_builder_2": "原始，非阻塞", "ext_sum_prompt_builder_3_desc": "扩展将使用常规主提示词生成器并将摘要请求添加为其作为最后的系统消息。", "ext_sum_prompt_builder_3": "经典，阻塞", "Summary Prompt": "摘要提示词", "ext_sum_restore_default_prompt_tip": "恢复默认提示词", "ext_sum_prompt_placeholder": "该提示词将被发送给 AI，以请求生成摘要。{{words}} 将解析为“字数”参数。", "ext_sum_target_length_1": "目标摘要长度", "ext_sum_target_length_2": "（", "ext_sum_target_length_3": "字）", "ext_sum_api_response_length_1": "API 响应长度", "ext_sum_api_response_length_2": "（", "ext_sum_api_response_length_3": "个词符）", "ext_sum_0_default": "“0”为默认", "ext_sum_raw_max_msg": "[原始] 每个请求的最大消息数", "ext_sum_0_unlimited": "“0”为无限制", "Update frequency": "更新频率", "ext_sum_update_every_messages_1": "更新间隔", "ext_sum_update_every_messages_2": "消息", "ext_sum_0_disable": "“0”为禁用", "ext_sum_auto_adjust_desc": "尝试根据聊天指标自动调整间隔。", "ext_sum_update_every_words_1": "更新间隔（", "ext_sum_update_every_words_2": "字）", "ext_sum_both_sliders": "如果两个滑块都不为零，则两者都将按照各自的间隔触发摘要更新。", "ext_sum_injection_template": "插入模板", "ext_sum_memory_template_placeholder": "{{summary}} 将解析当前摘要内容。", "ext_sum_injection_position": "插入位置", "ext_sum_include_wi_scan_desc": "在 WI 扫描中包括最新摘要。", "ext_sum_include_wi_scan": "纳入世界信息扫描", "None (not injected)": "无（未注入）", "ext_sum_injection_position_none": "摘要不会被注入到提示中。您仍然可以通过 {{summary}} 宏访问它。", "How many messages before the current end of the chat.": "当前聊天结束前还有多少条消息。", "Labels and Message": "标签和信息", "Label": "标签", "(label of the button, if no icon is chosen) ": "（如果没有选择图标，则为按钮的标签）", "Title": "标题", "(tooltip, leave empty to show message or /command)": "（工具提示，留空以显示消息或/命令）", "Message / Command:": "消息/命令：", "Word wrap": "自动换行", "Tab size:": "标签大小：", "Ctrl+Enter to execute": "使用 Ctrl+Enter 来执行", "Context Menu": "上下文菜单", "Chaining:": "链接：", "Auto-Execute": "自动执行", "Don't trigger auto-execute": "不触发自动执行", "Invisible (auto-execute only)": "隐形（仅自动执行）", "Execute on startup": "启动时执行", "Execute on user message": "根据用户消息执行", "Execute on AI message": "根据 AI 消息执行", "Execute on chat change": "聊天内容改变时执行", "Execute on new chat": "在新聊天中执行", "Execute on group member draft": "起草群组成员时执行", "Automation ID:": "自动化标识", "Testing": "测试", "Execute": "执行", "Quick Reply": "快速回复", "Enable Quick Replies": "启用快速回复", "Combine Quick Replies": "合并快速回复", "Show Popout Button": "（在电脑上）展示弹出式按钮", "Global Quick Reply Sets": "全局快速回复集", "Chat Quick Reply Sets": "聊天快速回复集", "Edit Quick Replies": "编辑快速回复", "Disable Send (Insert Into Input Field)": "禁用发送（插入输入字段）", "Place Quick Reply Before Input": "在输入前放置快速回复", "Inject user input automatically": "自动注入用户输入", "(if disabled, use ": "（如果禁用，使用", "macro for manual injection)": "宏用于手动注入）", "Color": "颜色", "Only apply color as accent": "仅应用颜色作为强调", "ext_regex_title": "正则", "ext_regex_new_global_script_desc": "新的全局正则表达式脚本", "ext_regex_new_global_script": "新建全局正则", "ext_regex_new_scoped_script_desc": "新的作用域正则表达式脚本", "ext_regex_new_scoped_script": "新建局部正则", "ext_regex_import_script": "导入正则", "ext_regex_global_scripts": "全局正则脚本", "ext_regex_global_scripts_desc": "影响所有角色，保存在本地设定中", "ext_regex_scoped_scripts": "局部正则脚本", "ext_regex_disallow_scoped": "不允许使用局部正则", "ext_regex_allow_scoped": "允许使用局部正则", "ext_regex_scoped_scripts_desc": "只影响当前角色，保存在角色卡片中", "Regex Editor": "正则表达式编辑器", "Test Mode": "测试模式", "ext_regex_desc": "“正则”是一个使用“正则表达式”来查找/替换字符串的工具。如果您想了解更多信息，请点击标题旁边的“？”。", "Input": "输入", "ext_regex_test_input_placeholder": "在此输入...", "Output": "输出", "ext_regex_output_placeholder": "空", "Script Name": "脚本名称", "Find Regex": "查找正则表达式", "Replace With": "替换为", "ext_regex_replace_string_placeholder": "使用 {{match}} 包含来自“查找正则表达式”或“$1”、“$2”等的匹配文本作为捕获组。", "Trim Out": "修剪掉", "ext_regex_trim_placeholder": "在替换之前全局修剪正则表达式匹配中任何不需要的部分。用回车键分隔每个元素。", "ext_regex_affects": "作用范围", "ext_regex_user_input_desc": "用户发送的消息", "ext_regex_user_input": "用户输入", "ext_regex_ai_input_desc": "从生成式API中获取的信息。", "ext_regex_ai_output": "AI输出", "ext_regex_slash_desc": "通过 STscript 命令发送的消息。", "Slash Commands": "快捷命令", "ext_regex_wi_desc": "知识书/世界书 条目的内容。需要勾选“仅格式提示词”！", "ext_regex_reasoning_desc": "推理块内容。当'仅格式提示词'被选中时，它会影响提示词里的推理内容。", "ext_regex_min_depth_desc": "当应用于提示或显示时，仅影响深度至少为 N 级的消息。“0”为最后一条消息，“1”为倒数第二条消息等。仅计算 WI 条目 @Depth 和可用消息，即非隐藏或系统消息。", "Min Depth": "最小深度", "ext_regex_min_depth_placeholder": "无限", "ext_regex_max_depth_desc": "当应用于提示词或显示时，仅影响深度不超过 N 级的消息。“0”为最后一条消息，“1”为倒数第二条消息等。仅计算世界信息条目 @Depth 和可用消息，即非隐藏或系统消息。", "ext_regex_other_options": "其他选项", "ext_regex_run_on_edit_desc": "当指定角色的消息被编辑时运行正则脚本。", "Run On Edit": "在编辑时运行", "ext_regex_substitute_regex_desc": "在运行正则表达式查找之前，替换 {{macros}}", "Macro in Find Regex": "正则表达式查找时的宏", "Don't substitute": "不替换", "Substitute (raw)": "替换（原始）", "Substitute (escaped)": "替换（转义）", "Ephemerality": "短暂", "ext_regex_only_format_visual_desc": "正则仅在聊天页面生效，聊天文件内的内容不会被改变。", "Only Format Display": "仅格式显示", "ext_regex_only_format_prompt_desc": "聊天记录不会改变，只有在请求发送时（生成时）才会出现提示词。", "Only Format Prompt (?)": "仅格式提示词", "ext_regex_import_target": "导入至：", "ext_regex_disable_script": "禁用脚本", "ext_regex_enable_script": "启用脚本", "ext_regex_edit_script": "编辑脚本", "ext_regex_move_to_global": "移至全局脚本", "ext_regex_move_to_scoped": "移至作用域脚本", "ext_regex_export_script": "导出脚本", "ext_regex_delete_script": "删除脚本", "Trigger Stable Diffusion": "触发Stable Diffusion", "Abort current image generation task": "中止当前图像生成", "Stop Image Generation": "停止图像生成", "sd_Yourself": "你自己", "sd_Your_Face": "你的脸", "sd_Me": "我", "sd_The_Whole_Story": "整个故事", "sd_The_Last_Message": "最后的信息", "sd_Raw_Last_Message": "原始最后一条消息", "sd_Background": "背景", "Image Generation": "图像生成", "sd_refine_mode": "允许在将提示词发送到生成 API 之前手动编辑提示词", "sd_refine_mode_txt": "生成之前编辑提示词", "sd_function_tool": "Use the function tool to automatically detect intents to generate images.", "sd_function_tool_txt": "Use function tool", "sd_interactive_mode": "发送消息时自动生成图像，例如“给我发一张猫的照片”。", "sd_interactive_mode_txt": "交互模式", "sd_multimodal_captioning": "使用多模态字幕根据用户和角色的头像生成提示词。", "sd_multimodal_captioning_txt": "使用多模态字幕来描绘肖像", "sd_free_extend": "使用当前选择的 LLM 自动扩展自由模式主题提示（不是肖像或背景）。", "sd_free_extend_txt": "延长自由模式提示", "sd_free_extend_small": "（交互/命令）", "sd_snap": "快照生成请求具有强制纵横比（肖像、背景）到最接近已知分辨率，同时尝试保留绝对像素数（推荐用于 SDXL）。", "sd_snap_txt": "自动调整分辨率", "sd_auto_url": "例如：{{auto_url}}", "Authentication (optional)": "身份验证（可选）", "Example: username:password": "例：用户名:密码", "Important:": "重要：", "sd_auto_auth_warning_1": "使用", "sd_auto_auth_warning_2": "注意！服务器必须可从 SillyTavern 主机访问。", "sd_drawthings_url": "例如：{{drawthings_url}}", "sd_drawthings_auth_txt": "运行 DrawThings 应用程序并在 UI 中启用 HTTP API 开关！必须可以从 SillyTavern 主机访问服务器。", "Model ID": "Model ID", "e.g. black-forest-labs/FLUX.1-dev": "例如：black-forest-labs/FLUX.1-dev", "sd_vlad_url": "例如：{{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "必须能够从 SillyTavern 主机访问该服务器。", "Hint: Save an API key in AI Horde API settings to use it here.": "提示：在 Horde AI API 设置中保存一个 API 密钥以便在此处使用它。", "Allow NSFW images from Horde": "允许来自 Horde 的 NSFW 图片", "Sanitize prompts (recommended)": "净化提示词（推荐）", "Automatically adjust generation parameters to ensure free image generations.": "自动调整生成参数，确保图像生成自由。", "Avoid spending Anlas": "避免花费 Anlas", "Opus tier": "（作品层）", "View my Anlas": "查看我的目录", "These settings only apply to DALL-E 3": "这些设置仅适用于 DALL-E 3", "Image Style": "图像风格", "Image Quality": "画面质量", "Standard": "标准", "HD": "高清", "sd_comfy_url": "例如：{{comfy_url}}", "Open workflow editor": "打开工作流编辑器", "Create new workflow": "创建新的工作流", "Delete workflow": "删除工作流", "Enhance": "提高", "API Key": "API 密钥", "Click to set": "点击设置", "You can find your API key in the Stability AI dashboard.": "您可以在 Stability AI 仪表板中找到您的 API 密钥。", "Style Preset": "风格预设", "Prompt Upsampling": "提示词增强（Upsampling）", "Sampling method": "采样方法", "Scheduler": "调度器", "Resolution": "分辨率", "Upscaler": "图像扩大器", "Sampling steps": "采样步数", "Width": "宽度", "Height": "高度", "Swap width and height": "交换宽度和高度", "Upscale by": "扩大倍数", "Denoising strength": "去噪强度", "Hires steps (2nd pass)": "高清修复步数（第二遍）", "CLIP Skip": "片段跳过", "Restore Faces": "面部修复", "Hires. Fix": "高清修复", "Karras": "<PERSON><PERSON><PERSON>", "Not all samplers supported.": "并非所有采样器都受支持。", "sd_adetailer_face": "Use ADetailer with face model during the generation. The ADetailer extension must be installed on the backend.", "Use ADetailer (Face)": "使用 ADetailer（脸部）", "SMEA versions of samplers are modified to perform better at high resolution.": "SMEA 版本的采样器经过修改，在高分辨率下性能更佳。", "SMEA": "中小企业协会", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "SMEA 采样器的 DYN 变体通常会产生更加多样化的输出，但在非常高的分辨率下可能会失败。", "DYN": "动态", "Decrisper": "去伪器", "(-1 for random)": "（“-1”为随机）", "Preset for prompt prefix and negative prompt": "提示词前缀和负面提示词的预设", "Style": "风格", "Save style": "保存风格", "Delete style": "删除风格", "Common prompt prefix": "常见提示词前缀", "sd_prompt_prefix_placeholder": "使用 {prompt} 指定生成的提示词插入的位置", "Negative common prompt prefix": "常见负面提示词前缀", "Character-specific prompt prefix": "特定角色的提示词前缀", "Won't be used in groups.": "不可供群聊使用。", "sd_character_prompt_placeholder": "描述当前选定角色的任何特征。将添加在常见提示词前缀后。\n示例：女性、绿眼睛、棕色头发、粉色衬衫", "Character-specific negative prompt prefix": "特定角色的负面提示词前缀", "sd_character_negative_prompt_placeholder": "所选角色不应出现的任何特征。将添加在常见负面提示词前缀后。\n示例：珠宝、鞋子、眼镜", "Shareable": "可共享", "Chat Message Visibility (by source)": "聊天消息可见性（按来源）", "Uncheck to hide the extension's messages in chat prompts.": "取消选中可在聊天提示中隐藏扩展消息。", "Extensions Menu": "扩展菜单", "Slash Command": "快捷命令", "Interactive Mode": "交互模式", "Function Tool": "Function Tool", "Image Prompt Templates": "图像提示模板", "Token Counter": "词符计数器", "Type / paste in the box below to see the number of tokens in the text.": "在下方框中输入或粘贴你想要统计词符数量的文本。", "Selected tokenizer:": "已选分词器：", "Input:": "输入：", "Tokens:": "词符：", "Tokenized text:": "词符化文本：", "Token IDs:": "词符ID：", "ext_translate_btn_chat": "翻译聊天", "ext_translate_btn_input": "翻译输入", "ext_translate_delete_confirm_1": "你确定吗？", "ext_translate_delete_confirm_2": "这将从当前聊天中的所有消息中删除翻译文本。此操作无法撤消。", "ext_translate_title": "聊天翻译", "ext_translate_auto_mode": "自动模式", "ext_translate_mode_none": "没有任何", "ext_translate_mode_responses": "翻译回复", "ext_translate_mode_inputs": "翻译输入", "ext_translate_mode_both": "翻译两者", "ext_translate_mode_provider": "提供者", "ext_translate_target_lang": "目标语言", "ext_translate_clear": "清空设置", "Select TTS Provider": "选择 文本转语音 的服务提供商", "tts_enabled": "已启用", "Narrate user messages": "朗读用户消息", "Auto Generation": "自动生成", "Requires auto generation to be enabled.": "需要启用自动生成功能。", "Narrate by paragraphs (when streaming)": "按段朗读（流式播放时）", "Narrate by paragraphs (when not streaming)": "按段朗读（非流式播放时）", "Only narrate quotes": "只朗读引号内文本", "Ignore text, even quotes, inside asterisk": "不朗读所有*星号内文本*，即使其被引号包裹", "Narrate only the translated text": "只朗读翻译后文本", "Skip codeblocks": "跳过代码块", "Skip tagged blocks": "跳过标签块里的内容（<标签>跳过这里</标签>）", "Pass Asterisks to TTS Engine": "将星号传递给文本转语音服务", "Audio Playback Speed": "音频播放速度", "Vector Storage": "向量存储", "Vectorization Source": "向量化源", "Local (Transformers)": "本地（Transformers）", "Vectorization Model": "向量化模型", "Keep model in memory": "将模型保存在内存中", "Hint: Set the URL in the API connection settings.": "提示：在 API 连接设置中设置 URL。", "The server MUST be started with the --embedding flag to use this feature!": "服务器必须使用 --embedding 标志启动才能使用此功能！", "NomicAI API Key": "NomicAI API 密钥", "Query messages": "查询消息", "Score threshold": "分数阈值", "Chunk boundary": "区块边界", "World Info settings": "世界信息设置", "Enable for World Info": "启用世界信息", "Enabled for all entries": "对所有条目启用", "Checked: all entries except ❌ status can be activated.": "勾选：除❌状态外的所有条目均可激活。", "Unchecked: only entries with ❌ status can be activated.": "未选中：只有具有🔗状态的条目才可以被激活。", "Max Entries": "最大条目数", "File vectorization settings": "文件向量化设置", "Enable for files": "为文件启用", "Only chunk on custom boundary": "仅按自定义边界分块", "Translate files into English before processing": "处理之前将文件翻译成英文", "Message attachments": "消息附件", "Size threshold (KB)": "大小阈值（KB）", "Chunk size (chars)": "块大小（字符）", "Chunk overlap (%)": "块重叠（%）", "Retrieve chunks": "检索块", "Data Bank files": "数据库文件", "Injection Template": "注入模板", "Injection Position": "注射位置", "Vectorize All": "全部向量化", "Purge Vectors": "清除向量", "Chat vectorization settings": "聊天向量化设置", "Enabled for chat messages": "已启用聊天消息", "Retain#": "保持＃", "Insert#": "插入＃", "Vector Summarization": "向量摘要", "Summarize chat messages for vector generation": "汇总聊天消息以生成向量", "Warning: This will slow down vector generation drastically, as all messages have to be summarized first.": "警告：这将大大减慢向量生成速度，因为必须先汇总所有消息。", "Summarize chat messages when sending": "发送时总结聊天消息", "Warning: This might cause your sent messages to take a bit to process and slow down response time.": "警告：这可能会导致您发送的消息需要一点时间来处理并减慢响应时间。", "Extras API": "附加 API", "Only used when Main API or WebLLM Extension is selected.": "仅在 Main API 或 WebLLM 扩展被选中时使用。", "Old messages are vectorized gradually as you chat. To process all previous messages, click the button below.": "随着您聊天，旧消息会逐渐向量化。\n要处理所有以前的消息，请单击下面的按钮。", "View Stats": "查看统计数据", "Manager Users": "管理用户", "New User": "新用户", "Status:": "地位：", "Created:": "创建时间：", "Display Name:": "显示名称：", "User Handle:": "用户句柄：", "Password:": "密码：", "Confirm Password:": "确认密码：", "This will create a new subfolder...": "这将在 /data/ 目录中创建一个新的子文件夹，以用户的句柄作为文件夹名称。", "Note:": "提示：", "this chat is temporary and will be deleted as soon as you leave it.": "此聊天会话是临时的，会在你离开时被删除。", "Enter a new display name:": "输入一个新的昵称：", "Current Password:": "当前密码：", "New Password:": "新密码：", "Confirm New Password:": "确认新密码：", "Import Tags For _begin": "为", "Import Tags For _end": "导入标签", "Click remove on any tag to remove it from this import.<br />Select one of the import options to finish importing the tags.": "单击任意标签上的“删除”可将其从本次导入中删除。\n选择其中一个导入选项以完成标签的导入。", "Existing Tags": "现有标签", "New Tags": "新标签", "Folder Tags": "文件夹标签", "The following tags will be auto-imported based on the currently selected folders": "根据当前选定的文件夹将自动导入以下标签", "Import None": "不导入", "Import All": "全部导入", "Import Existing": "导入现有", "Import": "导入", "Chat Lorebook": "聊天知识书", "Chat Lorebook for": "聊天知识书", "chat_world_template_txt": "选定的世界信息将绑定到此聊天。生成 AI 回复时，\n它将与全球和角色传说书中的条目相结合。", "chat_rename_1": "输入聊天的新名称：", "chat_rename_2": "注意！！与其他文件重名会导致错误！！", "chat_rename_3": "此举会将此聊天与标记为“检查点”的聊天解绑。", "chat_rename_4": "（不需要在结尾添加 '.JSONL' 后缀）", "Enter Checkpoint Name:": "输入检查点名称：", "(Leave empty to auto-generate)": "（留空以自动生成）", "The currently existing checkpoint will be unlinked and replaced with the new checkpoint, but can still be found in the Chat Management.": "当前检查点将会被解绑并替换为新的检查点，但仍可在聊天管理中找到。", "Include Body Parameters": "包括主体参数", "custom_include_body_desc": "聊天完成请求主体中要包含的参数（YAML 对象）\n\n示例：\n- top_k：20\n- repetition_penalty：1.1", "Exclude Body Parameters": "排除主体参数", "custom_exclude_body_desc": "要从聊天完成请求主体中排除的参数（YAML 数组）\n\n示例：\n- frequency_penalty\n- presence_penalty", "Include Request Headers": "包含请求标头", "custom_include_headers_desc": "聊天完成请求的附加标头（YAML 对象）\n\n示例：\n- CustomHeader：自定义值\n- AnotherHeader：自定义值", "Functions in this category are for advanced users only. Don't click anything if you're not sure about the consequences.": "此类别中的功能仅供高级用户使用。如果您不确定后果，请不要点击任何内容。", "THIS IS PERMANENT!": "此操作不可逆！", "Also delete the chat files": "同时删除聊天文件", "Are you sure you want to delete this user?": "您确定要删除该用户吗？", "Deleting:": "删除：", "Also wipe user data.": "同时清空用户数据", "Warning:": "警告：", "This action is irreversible.": "此操作不可逆。", "Type the user's handle below to confirm:": "在下面输入此用户的用户句柄以确认删除操作：", "Are you sure you want to duplicate this character?": "你确定要复制这个角色吗？", "If you just want to start a new chat with the same character...": "如果你只是想要与此角色开启一个新的聊天，只需点击聊天左下方菜单中的“开始新聊天”按钮。", "Forbid Media Override explanation": "当前角色/群聊成员使用外部媒体的能力。", "Forbid Media Override subtitle": "媒体：图像、视频、音频。外部：不在本地服务器上托管。", "forbid_media_global_state_forbidden": "（禁止）", "forbid_media_global_state_allowed": "（允许）", "Always forbidden": "始终禁止", "Always allowed": "始终允许", "help_format_1": "文本格式化命令：", "help_format_2": "*文本*", "help_format_3": "显示为", "help_format_4": "斜体", "help_format_5": "**文本**", "help_format_6": "显示为", "help_format_7": "大胆的", "help_format_8": "***文本***", "help_format_9": "显示为", "help_format_10": "粗斜体", "help_format_11": "__文本__", "help_format_12": "显示为", "help_format_13": "强调", "help_format_14": "~~文本~~", "help_format_15": "显示为", "help_format_16": "删除线", "help_format_17": "[文本](网址)", "help_format_18": "显示为", "help_format_19": "超级链接", "help_format_20": "![文本](网址)", "help_format_21": "显示为图像", "help_format_22": "```文本```", "help_format_23": "显示为代码块（反引号之间允许换行）", "help_format_like_this": "像这样", "help_format_24": "`文本`", "help_format_25": "显示为", "help_format_26": "内联代码", "help_format_27": "> 文本", "help_format_28": "显示为块引用（请注意 > 后面的空格）", "help_format_29": "# 文本", "help_format_30": "显示为大标题（注意空格）", "help_format_32": "## 文本", "help_format_33": "显示为中等标题（注意空格）", "help_format_35": "### 文本", "help_format_36": "显示为小标题（注意空格）", "help_1": "您好！请选择您想要详细了解的帮助主题：", "help_2": "斜线命令", "help_or": "或者", "help_3": "格式化", "help_4": "热键", "help_5": "{{宏}}", "help_6": "还有其他问题吗？", "help_7": "SillyTavern 官方文档网站", "help_8": "有更多信息！", "help_hotkeys_0": "热键/按键绑定", "help_hotkeys_1": "上", "help_hotkeys_2": "编辑聊天中的最后一条消息", "help_hotkeys_3": "Ctrl+上", "help_hotkeys_4": "编辑聊天中的最后一条用户消息", "help_hotkeys_5": "左", "help_hotkeys_6": "向左滑动", "help_hotkeys_7": "右", "help_hotkeys_8": "向右滑动（注意：当聊天栏中输入内容时，滑动热键将被禁用）", "help_hotkeys_9": "Enter", "help_hotkeys_10": "（选中聊天栏）", "help_hotkeys_10_1": "向 AI 发送消息", "help_hotkeys_11": "Ctrl+Enter", "help_hotkeys_12": "重新生成最后的 AI 响应", "help_hotkeys_13": "Alt+Enter", "help_hotkeys_14": "继续上一次AI响应", "help_hotkeys_15": "Esc", "help_hotkeys_16": "停止 AI 响应生成、关闭 UI 面板、取消消息编辑", "help_hotkeys_17": "Ctrl+Shift+上", "help_hotkeys_18": "滚动到上下文行", "help_hotkeys_19": "Ctrl+Shift+下", "help_hotkeys_20": "滚动聊天到底部", "help_hotkeys_21": "在输入框中生效，由这个图标标记：", "help_hotkeys_22": "**加粗**", "help_hotkeys_23": "*斜体*", "help_hotkeys_24": "__下划线__", "help_hotkeys_25": "`行内代码`", "help_hotkeys_26": "~~删除线~~", "Import Characters": "导入角色", "Enter the URL of the content to import": "输入要导入的内容的URL", "Supported sources:": "支持的来源：", "char_import_1": "Chub 角色（直链或ID）", "char_import_example": "例子：", "char_import_2": "Chub 知识书（直链或ID）", "char_import_3": "JanitorAI 角色（直链或UUID）", "char_import_4": "Pygmalion.chat 角色（直链或UUID）", "char_import_5": "AICharacterCards.com 角色（直链或ID）", "char_import_6": "被允许的PNG直链（请参阅", "char_import_7": "）", "char_import_8": "RisuRealm 角色（直链）", "char_import_9": "Soulkyn 角色（直链）", "char_import_10": "Perchance 角色（直链或UUID + .gz）", "Supports importing multiple characters.": "支持导入多个角色。", "Write each URL or ID into a new line.": "将每个 URL 或 ID 写入新行。", "Enter the Git URL of the extension to install": "输入扩展程序的 Git URL 以安装", "Disclaimer:": "免责声明：", "Please be aware that using external extensions can have unintended side effects and may pose security risks. Always make sure you trust the source before importing an extension. We are not responsible for any damage caused by third-party extensions.": "使用外部的扩展程序可能存在意料外的副作用和安全隐患。在导入扩展程序前，请一定确认其来源可信。我们不为第三方扩展程序造成的任何损失负责。", "Prompt Itemization": "提示词拆分", "Show Raw Prompt": "显示原始提示词", "Copy Prompt": "复制提示词", "Show Prompt Differences": "显示提示词差异", "API/Model:": "API/模型：", "Preset:": "预设：", "Tokenizer:": "分词器：", "Only the white numbers really matter. All numbers are estimates. Grey color items may not have been included in the context due to certain prompt format settings.": "只有白色的数字作数。所有数字均为估算。\n    灰色的数字可能因特定的提示词处理规则而被排除在外。", "System Info:": "系统信息：", "Prompt Tokens:": "提示词Token：", "World Info:": "世界书：", "Chat History:": "聊天记录：", "Extensions:": "扩展程序：", "Bias:": "Bias:", "Total Tokens in Prompt:": "提示词的总Token数量：", "Max Context": "最大上下文：", "(Context Size - Response Length)": "（上下文长度 - 回复长度）", "System-wide Replacement Macros (in order of evaluation):": "系统范围的替换宏（按评估顺序）：", "help_macros_1": "仅适用于斜线命令批处理。替换为上一个命令的返回结果。", "help_macros_2": "仅插入一个换行符。", "help_macros_3": "修剪此宏周围的换行符。", "help_macros_4": "没有操作，只是一个空字符串。", "help_macros_5": "API 设置中定义的全局提示。仅在高级定义提示覆盖中有效。", "help_macros_6": "用户输入", "help_macros_7": "角色的主提示覆盖", "help_macros_8": "角色越狱提示覆盖", "help_macros_9": "角色描述", "help_macros_10": "人物性格", "help_macros_11": "角色场景", "help_macros_12": "您当前的角色描述", "help_macros_13": "角色对话示例", "help_macros_14": "未格式化的对话示例", "(only for Story String)": "（仅适用于故事字符串）", "help_macros_summary": "“Summarize”扩展生成的最新聊天摘要（如果有）。", "help_macros_15": "您当前的用户角色名称", "help_macros_16": "角色的名字", "help_macros_17": "角色的版本号", "help_macros_18": "以逗号分隔的群成员名称列表或单人聊天中的角色名称。别名：{{charIfNotGroup}}", "help_groupNotMuted": "与 {{group}} 相同，但排除被禁言的成员", "help_macros_19": "当前选定的 API 的文本生成模型名称。", "Can be inaccurate!": "不一定准确！", "help_macros_20": "最新聊天消息的文本。", "help_macros_lastUser": "最后的用户聊天消息文本。", "help_macros_lastChar": "最后的角色聊天消息文本。", "help_macros_21": "最新聊天消息的索引号。对于斜线命令批处理很有用。", "help_macros_22": "上下文中包含的第一条消息的 ID。要求在当前会话中至少运行一次生成。", "help_macros_firstDisplayedMessageId": "第一条载入可见聊天的消息的ID", "help_macros_23": "最后一条聊天消息中当前滑动的 ID（以 1 为基数）。如果最后一条消息是用户或提示隐藏的，则为空字符串。", "help_macros_24": "最后一条聊天消息中的滑动次数。如果最后一条消息是用户隐藏或提示隐藏的，则为空字符串。", "help_macros_reverse": "反转宏的内容。", "help_macros_25": "您可以在此处留言，宏将被替换为空白内容。AI 看不到。", "help_macros_26": "当前时间", "help_macros_27": "当前日期", "help_macros_28": "当前工作日", "help_macros_29": "当前 ISO 时间（24 小时制）", "help_macros_30": "当前 ISO 日期 (YYYY-MM-DD)", "help_macros_31": "指定格式的当前日期/时间，例如德国日期/时间：", "help_macros_32": "指定 UTC 时区偏移量的当前时间，例如 UTC-4 或 UTC+2", "help_macros_33": "time1 和 time2 之间的时间差。接受时间和日期宏。（例如：{{timeDiff::{{isodate}} {{time}}::2024/5/11 12:30:00}}）", "help_macros_34": "距离上次用户消息发送的时间", "help_macros_35": "为 AI 设置行为偏差，直到下一个用户输入。文本周围的引号很重要。", "help_macros_36": "掷骰子。（例如：", "space_  will roll a 6-sided dice and return a number between 1 and 6)": "将掷一个 6 面骰子并返回 1 到 6 之间的数字）", "help_macros_37": "从列表中返回一个随机项目。（例如：", "space_  will return 1 of the 4 numbers at random. Works with text lists too.": "将随机返回 4 个数字中的 1 个。也适用于文本列表。", "help_macros_38": "随机的替代语法允许在列表项中使用逗号。", "help_macros_39": "从列表中随机挑选一项。工作原理与 {{random}} 相同，具有相同的语法选项，但一旦挑选，挑选将一直持续到本次聊天，不会在连续消息和提示处理中重新滚动。", "help_macros_40": "如果使用文本生成 WebUI 后端，则动态地将引号中的文本添加到禁用单词序列中。对其他后端不执行任何操作。可以在任何地方使用（角色描述、WI、AN 等）。文本周围的引号很重要。", "help_macros_isMobile": "当为移动端时为\"true\"，反之为\"false\"", "Instruct Mode and Context Template Macros:": "指导模式和上下文模板宏：", "(enabled in the Advanced Formatting settings)": "（在高级格式设置中启用）", "help_macros_41": "令牌中允许的最大提示长度 = （上下文长度 - 响应长度）", "help_macros_42": "上下文模板示例对话分隔符", "help_macros_43": "上下文模板聊天开始行", "help_macros_44": "主系统提示（如果选择，则覆盖字符提示，或 instructSystemPrompt）", "help_macros_45": "指示系统提示", "help_macros_46": "指示系统提示前缀序列", "help_macros_47": "指示系统提示后缀序列", "help_macros_48": "指示用户前缀序列", "help_macros_49": "指示用户后缀序列", "help_macros_50": "指导助理前缀序列", "help_macros_51": "指导助理后缀序列", "help_macros_52": "指导助理第一个输出序列", "help_macros_53": "指导助手最后输出序列", "help_macros_54": "指示系统消息前缀序列", "help_macros_55": "指示系统消息后缀序列", "help_macros_56": "指示系统指令前缀", "help_macros_57": "指示第一个用户消息填充器", "help_macros_58": "指示停止顺序", "help_macros_first_user": "指示用户第一个输入序列", "help_macros_last_user": "指示用户最后输入序列", "Chat variables Macros:": "聊天变量宏：", "Local variables = unique to the current chat": "局部变量 = 当前聊天所独有", "Global variables = works in any chat for any character": "全局变量 = 适用于任何角色的任何聊天", "Scoped variables = works in STscript": "范围变量 = 在 STscript 中有效", "help_macros_59": "替换为局部变量“name”的值", "help_macros_60": "替换为空字符串，将局部变量“name”设置为“value”", "help_macros_61": "替换为空字符串，将“increment”的数值添加到局部变量“name”", "help_macros_62": "替换为变量“name”的值增加 1 的结果", "help_macros_63": "替换为变量“name”的值减 1 的结果", "help_macros_64": "替换为全局变量“name”的值", "help_macros_65": "替换为空字符串，将全局变量“name”设置为“value”", "help_macros_66": "替换为空字符串，将“increment”的数值添加到全局变量“name”", "help_macros_67": "替换为全局变量“name”的值增加 1 的结果", "help_macros_68": "替换为全局变量“name”的值减 1 的结果", "help_macros_69": "替换为范围变量“name”的值", "help_macros_70": "用范围变量“name”的索引处的项目值（对于数组/列表或对象/字典）替换", "Choose what to export": "选择您想要导出什么：", "{{name}}": "{{name}}", "Choose what to import": "选择您想要导入什么：", "If necessary, you can later restore this chat file from the /backups folder": "若需要，您可稍后在 /backups 文件夹中恢复此聊天文件。", "Also delete the current chat file": "同时删除当前聊天文件", "Persona Lorebook for": "Persona Lorebook for", "persona_world_template_txt": "A selected World Info will be bound to this persona. When generating an AI reply,\n            it will be combined with the entries from global, character and chat lorebooks.", "Export for character": "导出角色", "Export prompts for this character, including their order.": "导出此角色的提示词，包括其顺序。", "Export all": "全部导出", "Export all your prompts to a file": "将所有提示词导出到文件", "Insert prompt": "插入提示词", "Import a prompt list": "导入提示词列表", "Export this prompt list": "导出此提示词列表", "Reset current character": "重置当前角色", "New prompt": "新提示词", "Prompts": "提示词", "Total Tokens:": "总词符数：", "prompt_manager_tokens": "词符", "Are you sure you want to connect to the following proxy URL?": "你确定要连接到下面的代理URL吗？", "Encountered an error while processing your request.": "处理请求时遇到了问题。", "Check you have credits available on your": "检查您的 ", "OpenAI account quora_error": "OpenAI 账号余额是否充足", "dot quota_error": "。", "If you have sufficient credits, please try again later.": "若您有足够的余额，请稍后再试。", "Are you sure you want to reset your settings to factory defaults?": "您确定要将您的设置重置为出厂默认设置吗？", "Don't forget to save a snapshot of your settings before proceeding.": "在继续之前，不要忘记保存您的设置快照。", "Enter your password below to confirm:": "输入您的密码以确认：", "Chat Scenario Override": "聊天场景覆盖", "Remove": "移除", "Unique to this chat.": "仅对此聊天生效。", "All group members will use the following scenario text instead of what is specified in their character cards.": "All group members will use the following scenario text instead of what is specified in their character cards.", "The following scenario text will be used instead of the value set in the character card.": "The following scenario text will be used instead of the value set in the character card.", "Checkpoints inherit the scenario override from their parent, and can be changed individually after that.": "Checkpoints inherit the scenario override from their parent, and can be changed individually after that.", "Settings Snapshots": "设置快照", "Record a snapshot of your current settings.": "记录当前设置的快照。", "Make a Snapshot": "制作快照", "Restore this snapshot": "恢复此快照", "Download Model": "下载模型", "Downloader Options": "下载器选项", "Extra parameters for downloading/HuggingFace API": "下载/HuggingFace API 的额外参数。如果不确定，请将其留空。", "Revision": "修订", "Folder Name": "输出文件夹名称", "HF Token": "HF代币", "Include Patterns": "包含模式", "Glob patterns of files to include in the download.": "要包含在下载中的文件的全局模式。每个模式用换行符分隔。", "Exclude Patterns": "排除模式", "Glob patterns of files to exclude in the download.": "下载中要排除的文件的 Glob 模式。每个模式用换行符分隔。", "Tag Management": "标签管理", "Save your tags to a file": "将标签保存为文件", "Restore tags from a file": "从文件中恢复标签", "Create a new tag": "新建一个标签", "Drag handle to reorder. Click name to rename. Click color to change display.": "拖拽左侧三条横线以排序，点击名字以重命名，点击调色盘以切换颜色。", "Click on the folder icon to use this tag as a folder.": "点击文件夹图标来将此标签作为一个文件夹。", "Use alphabetical sorting": "按字母顺序排列", "tags_sorting_desc": "启用后，标签在创建或重命名时会自动按字母顺序排序。\n禁用后，新标签会追加到末尾。\n\n如果通过拖动手动重新排列标签，则自动排序将被禁用。", "Are you sure you want to delete the theme?": "你确定要删除这个主题吗？", "Hi,": "嗨，", "To enable multi-account features, restart the SillyTavern server with": "要启用多帐户功能，请使用以下命令重新启动 SillyTavern 服务器", "set to true in the config.yaml file.": "在 config.yaml 文件中设置为 true。", "Account Info": "帐户信息", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "要更改您的用户头像，请使用下面的按钮或在角色管理菜单中选择一个默认角色。", "Set your custom avatar.": "设置您的自定义头像。", "Remove your custom avatar.": "删除您的自定义头像。", "Handle:": "账号：", "This account is password protected.": "此帐户受密码保护。", "This account is not password protected.": "此帐户没有密码保护。", "Account Actions": "帐户操作", "Change Password": "修改密码", "Manage your settings snapshots.": "管理您的设置快照。", "Download a complete backup of your user data.": "下载您所有的用户数据的完整备份。", "Download Backup": "下载备份", "Danger Zone": "危险操作", "Reset your settings to factory defaults.": "将您的设置重置为默认值。", "Reset Settings": "重置设置", "Wipe all user data and reset your account to factory settings.": "删除所有用户数据并将您的账号重置为默认设置。", "Reset Everything": "重置一切", "This will delete all your settings and data. There will be no undo button. Make sure you have a backup before proceeding.": "This will delete all your settings and data. There will be no undo button.\n        Make sure you have a backup before proceeding.", "Account reset code has been posted to the server console.": "Account reset code has been posted to the server console.", "Reset Code:": "重置代码：", "Want to update?": "获取最新版本", "How to start chatting?": "如何快速开始聊天？", "Click _space": "点击", "and connect to an": "并连接一个", "and pick a character.": "并选择一个角色。", "You can add more": "你可以点击右侧添加更多", "or_welcome": "或从其他网站中", "from other websites": "。", "Go to the": "您可前往此处", "menu within": "（在这里：", "to install additional features.": "）以安装拓展功能。", "Confused or lost?": "遇到了不懂的内容？", "click these icons!": "点击左侧这种图标！", "in the chat bar": "至聊天框", "SillyTavern Documentation Site": "访问 SillyTavern 帮助文档", "Still have questions?": "仍有疑问？", "Join the SillyTavern Discord": "加入 SillyTavern 的 Discord群组", "Post a GitHub issue": "在 GitHub 发布问题", "Contact the developers": "联系开发者", "If you're connected to an API, try asking me something!": "若您已经配置好API，尝试发送些什么吧！", "Title/Memo": "标题（备忘）", "Strategy": "触发策略", "Position": "插入位置", "Trigger %": "触发概率%", "Generate Caption": "生成图片描述", "(DEPRECATED)": "（已弃用）", "[Currently loaded]": "[当前加载]", "Change Persona Image": "更改角色图片", "Delete Persona": "删除角色", "Duplicate Persona": "复制角色", "Enter a name for this persona:": "输入角色名", "Enable web search": "启用联网搜索", "Current Persona": "当前角色", "Global Settings": "全局设置", "Select a model": "选择模型", "Thinking...": "思考中", "Valid": "有效", "Rename Persona": "重命名角色", "Sort By: Name (Z-A)": "排序: 名称（Z-A）", "Sort By: Name (A-Z)": "排序: 名称（A-Z）", "Sort By: Date (Oldest First)": "排序: 日期（从最远到最新）", "Sort By: Date (Newest First)": "排序: 日期（从最新到最远）", "Set the reasoning block of a message. Returns the reasoning block content.": "设置消息的推理块。返回推理块内容。", "Select providers. No selection = all providers.": "选择服务商。未选择 = 所有服务商。"}