{"dependencies": {"@adobe/css-tools": "^4.4.3", "@agnai/sentencepiece-js": "^1.1.1", "@agnai/web-tokenizers": "^0.1.3", "@iconfu/svg-inject": "^1.2.3", "@jimp/core": "^1.6.0", "@jimp/js-bmp": "^1.6.0", "@jimp/js-gif": "^1.6.0", "@jimp/js-tiff": "^1.6.0", "@jimp/plugin-blit": "^1.6.0", "@jimp/plugin-circle": "^1.6.0", "@jimp/plugin-color": "^1.6.0", "@jimp/plugin-contain": "^1.6.0", "@jimp/plugin-cover": "^1.6.0", "@jimp/plugin-crop": "^1.6.0", "@jimp/plugin-displace": "^1.6.0", "@jimp/plugin-fisheye": "^1.6.0", "@jimp/plugin-flip": "^1.6.0", "@jimp/plugin-mask": "^1.6.0", "@jimp/plugin-quantize": "^1.6.0", "@jimp/plugin-resize": "^1.6.0", "@jimp/plugin-rotate": "^1.6.0", "@jimp/plugin-threshold": "^1.6.0", "@jimp/wasm-avif": "^1.6.0", "@jimp/wasm-jpeg": "^1.6.0", "@jimp/wasm-png": "^1.6.0", "@jimp/wasm-webp": "^1.6.0", "@mozilla/readability": "^0.6.0", "@popperjs/core": "^2.11.8", "@zeldafan0225/ai_horde": "^5.2.0", "archiver": "^7.0.1", "bing-translate-api": "^4.1.0", "body-parser": "^1.20.2", "bowser": "^2.11.0", "bytes": "^3.1.2", "chalk": "^5.4.1", "command-exists": "^1.2.9", "compression": "^1.8.1", "cookie-parser": "^1.4.6", "cookie-session": "^2.1.1", "cors": "^2.8.5", "crc": "^4.3.2", "csrf-sync": "^4.2.1", "diff-match-patch": "^1.0.5", "dompurify": "^3.2.6", "droll": "^0.2.1", "env-paths": "^3.0.0", "express": "^4.21.0", "form-data": "^4.0.4", "fuse.js": "^7.1.0", "google-translate-api-browser": "^3.0.1", "google-translate-api-x": "^10.7.2", "handlebars": "^4.7.8", "helmet": "^8.1.0", "highlight.js": "^11.11.1", "html-entities": "^2.6.0", "iconv-lite": "^0.6.3", "ip-matching": "^2.1.2", "ip-regex": "^5.0.0", "ipaddr.js": "^2.2.0", "is-docker": "^3.0.0", "localforage": "^1.10.0", "lodash": "^4.17.21", "mime-types": "^3.0.1", "moment": "^2.30.1", "morphdom": "^2.7.5", "multer": "^2.0.2", "node-fetch": "^3.3.2", "node-persist": "^4.0.4", "open": "^10.2.0", "png-chunk-text": "^1.0.0", "png-chunks-extract": "^1.0.0", "proxy-agent": "^6.5.0", "rate-limiter-flexible": "^5.0.5", "response-time": "^2.3.4", "sanitize-filename": "^1.6.3", "seedrandom": "^3.0.5", "showdown": "^2.1.0", "sillytavern-transformers": "2.14.6", "simple-git": "^3.28.0", "slidetoggle": "^4.0.0", "tiktoken": "^1.0.21", "url-join": "^5.0.0", "vectra": "^0.2.2", "wavefile": "^11.0.0", "webpack": "^5.98.0", "write-file-atomic": "^5.0.1", "ws": "^8.18.3", "yaml": "^2.8.0", "yargs": "^17.7.1", "yauzl": "^3.2.0"}, "engines": {"node": ">= 18"}, "overrides": {"vectra": {"openai": "^4.17.0"}, "axios": {"follow-redirects": "^1.15.4"}, "node-fetch": {"whatwg-url": "^14.0.0"}}, "name": "silly<PERSON>vern", "type": "module", "license": "AGPL-3.0", "repository": {"type": "git", "url": "https://github.com/SillyTavern/SillyTavern.git"}, "version": "1.13.2", "scripts": {"start": "node server.js", "debug": "node --inspect server.js", "start:global": "node server.js --global", "start:electron": "cd ./src/electron && npm run start", "start:deno": "deno run --allow-run --allow-net --allow-read --allow-write --allow-sys --allow-env server.js", "start:bun": "bun server.js", "start:no-csrf": "node server.js --disableCsrf", "postinstall": "node post-install.js", "lint": "eslint \"src/**/*.js\" \"public/**/*.js\" ./*.js", "lint:fix": "eslint \"src/**/*.js\" \"public/**/*.js\" ./*.js --fix", "plugins:update": "node plugins update", "plugins:install": "node plugins install"}, "bin": {"sillytavern": "./src/server-global.js"}, "rules": {"no-path-concat": "off", "no-var": "off"}, "main": "server.js", "devDependencies": {"@types/archiver": "^6.0.3", "@types/bytes": "^3.1.5", "@types/command-exists": "^1.2.3", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/cookie-session": "^2.0.49", "@types/cors": "^2.8.19", "@types/deno": "^2.3.0", "@types/express": "^4.17.23", "@types/jquery": "^3.5.32", "@types/jquery-cropper": "^1.0.4", "@types/jquery.transit": "^0.9.33", "@types/jqueryui": "^1.12.24", "@types/lodash": "^4.17.20", "@types/mime-types": "^3.0.1", "@types/multer": "^2.0.0", "@types/node": "^18.19.84", "@types/node-persist": "^3.1.8", "@types/png-chunk-text": "^1.0.3", "@types/png-chunks-extract": "^1.0.2", "@types/response-time": "^2.3.9", "@types/select2": "^4.0.63", "@types/toastr": "^2.1.43", "@types/write-file-atomic": "^4.0.3", "@types/yargs": "^17.0.33", "@types/yauzl": "^2.10.3", "eslint": "^8.57.1", "eslint-plugin-jsdoc": "^48.10.0"}}