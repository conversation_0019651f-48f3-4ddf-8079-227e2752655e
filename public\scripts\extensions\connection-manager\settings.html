<div class="wide100p">
    <div class="flex-container alignItemsBaseline">
        <h3 class="margin0">
            <span data-i18n="Connection Profile">Connection Profile</span>
            <a href="https://docs.sillytavern.app/usage/core-concepts/connection-profiles" target="_blank" class="notes-link">
                <span class="fa-solid fa-circle-question note-link-span"></span>
            </a>
        </h3>
        <i id="connection_profile_spinner" class="fa-solid fa-spinner fa-spin hidden"></i>
    </div>
    <div class="flex-container">
        <select class="text_pole flex1" id="connection_profiles"></select>
        <i id="view_connection_profile" class="menu_button fa-solid fa-info-circle" title="View connection profile details" data-i18n="[title]View connection profile details"></i>
        <i id="create_connection_profile" class="menu_button fa-solid fa-file-circle-plus" title="Create a new connection profile" data-i18n="[title]Create a new connection profile"></i>
        <i id="update_connection_profile" class="menu_button fa-solid fa-save" title="Update a connection profile" data-i18n="[title]Update a connection profile"></i>
        <i id="edit_connection_profile" class="menu_button fa-solid fa-pencil" title="Edit a connection profile" data-i18n="[title]Edit a connection profile"></i>
        <i id="reload_connection_profile" class="menu_button fa-solid fa-recycle" title="Reload a connection profile" data-i18n="[title]Reload a connection profile"></i>
        <i id="delete_connection_profile" class="menu_button fa-solid fa-trash-can" title="Delete a connection profile" data-i18n="[title]Delete a connection profile"></i>
    </div>
    <div id="connection_profile_details_content" class="hidden"></div>
</div>
