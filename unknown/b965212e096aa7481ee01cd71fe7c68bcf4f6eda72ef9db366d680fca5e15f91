/* Extensions */
#extensions_url {
    display: block;
}

.extensions_block input[type="submit"]:hover {
    background-color: green;
}

.extensions_block input[type="checkbox"],
.extensions_block input[type="radio"] {
    margin-left: 10px;
    margin-right: 10px;
}

label[for="extensions_autoconnect"] {
    display: flex;
    align-items: center;
    margin: 0 !important;
}

.extensions_url_block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.extensions_url_block h4 {
    display: inline;
}

.extensions_block {
    clear: both;
    padding: 0.05px;
}

.extensions_info {
    text-align: left;
}

.extensions_info h3 {
    margin-bottom: 0.5em;
}

.extensions_info h4 {
    margin-bottom: 0.5em;
}

.extensions_info p {
    margin-bottom: 0.5em;
    margin-left: 1em;
}

.extensions_info .disabled {
    color: lightgray;
}

.extensions_info .toggle_enable {
    color: lightgreen;
}

.extensions_info .toggle_disable {
    color: rgb(238, 144, 144);
}

.extensions_info .extension_enabled {
    font-weight: bold;
}

.extensions_info .extension_disabled {
    color: lightgray;
}

.extensions_info .extension_missing {
    color: gray;
}

.extensions_info .extension_modules {
    font-size: 0.8em;
    font-weight: normal;
}

.extensions_info .extension_block {
    display: flex;
    flex-wrap: nowrap;
    padding: 5px;
    margin-bottom: 5px;
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 10px;
    align-items: baseline;
    justify-content: space-between;
    gap: 5px;
}

.extensions_info .extension_name {
    font-size: 1.05em;
}

.extensions_info .extension_version {
    opacity: 0.8;
    font-size: 0.8em;
    font-weight: normal;
    margin-left: 2px;
}

.extensions_info .extension_block a {
    color: var(--SmartThemeBodyColor);
}

.extensions_info .extension_name.update_available {
    color: limegreen;
}

input.extension_missing[type="checkbox"] {
    opacity: 0.5;
}

.update-button {
    margin-right: 10px;
    display: inline-flex;
}

/* Fixes order of settings for extensions */
#extensions_settings,
#extensions_settings2 {
    display: flex;
    flex-direction: column;
}

/* Fixes order of settings for extensions */
.extension_container {
    display: contents;
}

#extensionsMenu>div.extension_container:empty {
    display: none;
}

.extensions_info .extension_text_block {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.extensions_info .extension_actions {
    flex-wrap: nowrap;
}

.extensions_toolbar {
    top: 0;
    position: sticky;
    display: flex;
    flex-direction: row;
    background-color: var(--SmartThemeBlurTintColor);
    gap: 5px;
    z-index: 1;
    margin-bottom: 10px;
    padding: 5px;
}
