#rm_print_characters_block.group_overlay_mode_select .character_select {
    transition: background-color var(--animation-duration-3x) ease;
    background-color: rgba(170, 170, 170, 0.15);
}

#rm_print_characters_block.group_overlay_mode_select .bogus_folder_select,
#rm_print_characters_block.group_overlay_mode_select .group_select {
    cursor: auto;
    filter: saturate(0.3);
}

#rm_print_characters_block.group_overlay_mode_select .bogus_folder_select:hover,
#rm_print_characters_block.group_overlay_mode_select .group_select:hover {
    background: none;
}

#rm_print_characters_block.group_overlay_mode_select .character_select input.bulk_select_checkbox {
    display: none !important;
}

#rm_print_characters_block.group_overlay_mode_select .character_select.character_selected {
    background-color: var(--SmartThemeQuoteColor);
}

#rm_print_characters_block.group_overlay_mode_select .character_select .bulk_select_checkbox {
    visibility: hidden;
    height: 0 !important;
}

#character_context_menu.hidden {
    display: none;
}

#character_context_menu {
    position: absolute;
    padding: 3px;
    z-index: 9998;
    background-color: var(--black90a);
    border: 1px solid var(--black90a);
    border-radius: 10px;
}

#character_context_menu ul li button {
    border: 0;
    border-bottom-color: currentcolor;
    color: var(--SmartThemeQuoteColor);
    background-color: transparent;
    font-weight: bold;
    font-size: 1em;
    padding: 0.5em;
    border-bottom: 1px dotted var(--SmartThemeQuoteColor);
    width: 100%;
    cursor: pointer;
}

#character_context_menu ul li button:hover {
    background-color: var(--SmartThemeBlurTintColor);
}

#character_context_menu ul li:last-child button {
    border-bottom: 0;
}

#character_context_menu ul li #character_context_menu_delete {
    color: var(--fullred);
}

#character_context_menu ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

#character_context_menu .character_context_menu_separator {
    height: 1px;
    background-color: var(--SmartThemeBotMesBlurTintColor);
}

#character_context_menu li:hover {
    background-color: var(--SmartThemeBotMesBlurTintColor);
}

#bulkEditButton.bulk_edit_overlay_active {
    color: var(--golden);
}

#bulk_tag_shadow_popup {
    backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 2));
    -webkit-backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 2));
    background-color: var(--black30a);
    position: absolute;
    width: 100%;
    height: 100vh;
    height: 100dvh;
    z-index: 9998;
    top: 0;
}

#bulk_tag_shadow_popup #bulk_tag_popup {
    padding: 1em;
}

#bulk_tag_shadow_popup #bulk_tag_popup #dialogue_popup_controls .menu_button {
    width: unset;
    padding: 0.25em;
}
