{"compilerOptions": {"module": "ESNext", "target": "ES2023", "moduleResolution": "<PERSON><PERSON><PERSON>", "strictNullChecks": true, "strictFunctionTypes": true, "checkJs": true, "allowUmdGlobalAccess": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "strictBindCallApply": true}, "exclude": ["**/node_modules/**", "**/dist/**", "**/.git/**", "public/**", "backups/**", "data/**", "cache/**", "src/tokenizers/**", "docker/**"]}